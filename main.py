""" 
    <AUTHOR>
    @abstract Api end point
    @since 01-23-2024
"""

from api import app, socketio
from api.initializeDb import setup_database,init_default_data, print_password_hash


if __name__ == '__main__':
    with app.app_context():
        # print_password_hash("disrupt123")
        # setup_database()
        # init_default_data(app)
        # app.run(debug=True, host='0.0.0.0', port=8000)

        #Use socketio.run instead of app.run for WebSocket support
        socketio.run(app, debug=True, host='0.0.0.0', port=8000)
