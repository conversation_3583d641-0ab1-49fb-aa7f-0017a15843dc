import subprocess
import os
import smtplib
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import ssl
from api import app

def send_mail(receivers, subject, body, cc_emails=None, attachment_path=None):
    smtp_server = app.config["smtp_server"]
    smtp_port = app.config["smtp_port"]
    smtp_username = app.config["smtp_username"]
    smtp_password = app.config["smtp_password"]

    # Create the message
    msg = MIMEMultipart()
    msg['From'] = smtp_username
    msg['To'] = ", ".join(receivers)
    if cc_emails:
        msg['Cc'] = ", ".join(cc_emails)
    msg['Subject'] = subject

    # Add body
    msg.attach(MIMEText(body, 'plain'))

    # Add attachment with better compression for large files
    if attachment_path:
        send_attachment_path = attachment_path
        
        file_size_mb = os.path.getsize(attachment_path) / (1024 * 1024)
        print(f"Original file size: {file_size_mb:.2f} MB")
        
        # If file is large and is a PDF, try to compress it
        compressed_path = None
        if file_size_mb > 1 and attachment_path.lower().endswith('.pdf'):
            try:
                # Try aggressive compression with Ghostscript
                print("Attempting PDF compression with Ghostscript...")
                gs_path = "gswin64c"  # Windows Ghostscript command
                if os.name != 'nt':  # Linux/Mac
                    gs_path = "gs"
                output_path = attachment_path + ".gs"

                subprocess.run([
                    gs_path, "-sDEVICE=pdfwrite", "-dCompatibilityLevel=1.4",
                    "-dPDFSETTINGS=/ebook", "-dNOPAUSE", "-dQUIET", "-dBATCH",
                    f"-sOutputFile={output_path}", attachment_path
                ], check=True)

                if os.path.exists(output_path):
                    gs_size = os.path.getsize(output_path) / (1024 * 1024)
                    original_size = os.path.getsize(attachment_path) / (1024 * 1024)
                    print(f"Compressed PDF with Ghostscript to {gs_size:.2f} MB")

                    if gs_size < original_size:
                        compressed_path = output_path
                    else:
                        print("Compression didn't reduce file size, using original")
                        os.remove(output_path)


                # Use the compressed file instead if it's smaller
                if compressed_path:
                    send_attachment_path = compressed_path

            except Exception as e:
                print(f"Compression failed: {e}")
                # Continue with original file if compression fails
                if compressed_path and os.path.exists(compressed_path):
                    try:
                        os.remove(compressed_path)
                    except:
                        pass
                compressed_path = None
        
        # If file is still too large for email (>19MB), return an error
        if os.path.getsize(send_attachment_path) / (1024 * 1024) > 19:
            print(f"File too large for email: {os.path.getsize(send_attachment_path) / (1024 * 1024):.2f} MB")
            return False
        
        # Attach the file
        with open(send_attachment_path, "rb") as attachment:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(attachment.read())
            encoders.encode_base64(part)
            part.add_header('Content-Disposition', f'attachment; filename="{os.path.basename(attachment_path)}"')
            msg.attach(part)
        
        # Clean up compressed file if it was created
        if compressed_path and os.path.exists(compressed_path):
            try:
                os.remove(compressed_path)
            except:
                pass
                
        if attachment_path and os.path.exists(attachment_path):
            try:
                os.remove(attachment_path)
            except:
                pass

    try:
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL(smtp_server, smtp_port, context=context) as server:
            server.login(smtp_username, smtp_password)
            server.sendmail(
                smtp_username,
                receivers + (cc_emails or []),
                msg.as_string()
            )
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False
 