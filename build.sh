#!/bin/bash

set -e 

project="annotation"

image_tag=${2}
image_name="me-west1-docker.pkg.dev/hse-projects-447312/disrupt/${project}-backend"
container_name="${project}-backend"
branch="main"

SYSTEM_ARCH=`dpkg --print-architecture`

check_tag_input() {
    if [ -z "${1}" ]; then
        echo "ERROR: Image Tag input must be provided"
        show_help
    fi
}

get_container_id() {
    docker ps -a --filter "name=${container_name}" --format {{.ID}}
}

create_docker_image(){
    if [ "${SYSTEM_ARCH}" == "amd64" ]; then
        PLATFORM="linux/amd64"
    else
        PLATFORM="linux/arm64"
    fi

    echo "Creating docker image [ ${image_name}:${image_tag} ]"
    docker build --platform ${PLATFORM} -t ${image_name}:${image_tag} .
}

create_container() {

    DIR="/usr/local/disruptlabs/${image_name}"

    if [ ! -d "$DIR" ]; then
        sudo mkdir -p "$DIR"
        echo "Directory $DIR created."
    fi

    configfile="config.cfg"
    sudo cp ./${configfile} ${DIR}/
    configfilehostpath="${DIR}/${configfile}"
    
    google_service_file="service_account.json"
    sudo cp ./${google_service_file} ${DIR}/
    google_service_filehostpath="${DIR}/${google_service_file}"

    echo "Creating docker container from image ->  ${image_name}:${image_tag}"
    docker create -it \
           --name ${container_name} \
           -e APPLICATION_TYPE=CONTAINERISED \
           -p 8008:8000 \
           --log-opt max-size=10m \
           --log-opt max-file=3 \
           --volume ${configfilehostpath}:/usr/local/disruptlabs/uam_backend/${configfile} \
           --volume ${google_service_filehostpath}:/usr/local/disruptlabs/uam_backend/${google_service_file} \
           ${image_name}:${image_tag}
}


delete_image() {
    docker image rm -f ${image_name}:${image_tag}
}

stop_container() {
    id=`get_container_id` 
    if [ -n "$id" ]; then
        echo "Stop container ${id}"
        docker stop ${id}
    fi
}

delete_container() {
    id=`get_container_id` 
    if [ -n "$id" ]; then
        echo "Delete container ${id}"
        docker rm ${id}
    fi
}

start_container() {
    id=`get_container_id` 
    if [ -n "$id" ]; then
        echo "Starting container ${id}"
        docker restart ${id}
    fi
}

show_logs() {
    id=`get_container_id` 
    if [ -n "$id" ]; then
        docker logs -f ${id}
    fi
}

show_help() {
    echo "Usage -> ${0} <command> <tag>"
    echo "--------------------------------Help--------------------------------------"
    echo "all   <TAG>              : Quick one step for all"
    echo "image  <TAG>             : Build docker image. Provide the image tag."
    echo "create_container  <TAG>  : Create docker container from the image"
    echo "start_container          : Start container"
    echo "deploy                   : Update git pull, build image, push image"
    echo "show_logs                : Show the logs of container"
    echo "help                     : Display this help."
    exit
}

## ENTRY
if [ -z ${1} ]; then
    show_help
fi

case "${1}" in
"image")
    check_tag_input ${2}
    create_docker_image
;;
"create_container")
    check_tag_input ${2}
    create_container
;;
"start_container")
    start_container
;;
"deploy")
    check_tag_input ${2}
    git pull origin ${branch}
    create_docker_image
    docker push ${image_name}:${image_tag}
;;
"show_logs")
    show_logs
;;
"all")
    check_tag_input ${2}
    stop_container
    delete_container
    delete_image
    create_docker_image
    create_container
    start_container
    show_logs
;;
"help")
    show_help
;;
"*")
    show_help
;;
esac
