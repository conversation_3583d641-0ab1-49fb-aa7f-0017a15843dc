name: Docker Image CI

on:
  push:
    branches: 
    - main
    - stage

jobs:

  build-prod:
    if: github.ref == 'refs/heads/main'
    runs-on: disruptlabs

    steps:
    
    - uses: actions/checkout@v4
    
    - name: Get current date as tag
      id: annotationtag
      run: echo "tag=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
      
    - name: Build the Docker image
      run: docker build . --file Dockerfile --tag me-west1-docker.pkg.dev/hse-projects-447312/disrupt/annotation-backend:${{ steps.annotationtag.outputs.tag }}
    
    - name: Push the image to GCP Artifact 
      run: docker push me-west1-docker.pkg.dev/hse-projects-447312/disrupt/annotation-backend:${{ steps.annotationtag.outputs.tag }}
    
    - name: Call the webhook
      run: |
          curl -X POST https://webhook.disruptlabs.tech/deploy_containerised_app \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer df21gr412gfdr4gf" \
          -d '{"project":"annotation","tag":"${{ steps.annotationtag.outputs.tag }}"}'
     
  build-stage:
    if: github.ref == 'refs/heads/stage'
    runs-on: disruptlabs

    steps:
    
    - uses: actions/checkout@v4
    
    - name: Get current date as tag
      id: safetylenstag
      run: echo "tag=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
      
    - name: Build the Docker image
      run: docker build . --file Dockerfile --tag me-west1-docker.pkg.dev/hse-projects-447312/disrupt/safetylens-backend:${{ steps.safetylenstag.outputs.tag }}
    
    - name: Push the image to GCP Artifact 
      run: docker push me-west1-docker.pkg.dev/hse-projects-447312/disrupt/safetylens-backend:${{ steps.safetylenstag.outputs.tag }}
    
    - name: Call the webhook
      run: |
          curl -X POST https://stage-webhook.disruptlabs.tech/deploy_containerised_app \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer df21gr412gfdr4gf" \
          -d '{"project":"safetylens","tag":"${{ steps.safetylenstag.outputs.tag }}"}'