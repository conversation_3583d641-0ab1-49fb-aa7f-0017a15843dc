from sqlalchemy import String, and_, func, or_, case

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.hseModels import *
from api.utils.utils import *

from api import db
from sqlalchemy.sql import cast
from sqlalchemy.types import String, JSON
from api.utils.utils import roles
from sqlalchemy.orm import joinedload,contains_eager
from sqlalchemy.dialects.postgresql import ARRAY 
from datetime import datetime, timedelta, timezone
import os
from werkzeug.utils import secure_filename
from api import config_parser
from zoneinfo import ZoneInfo
import hashlib



import requests

def add_new_camera(data):
    
    verified_modules = Modules.get_ids_by_list(list(set(data["modules"])))
    if len(verified_modules) == 0 :
        return {"success": False, "message": "Modules does not exist"}
    else:
        new_camera= Cameras.create(data,verified_modules)
        return {
            "success": True,
            "message": "Camera added successfully",
            "data":{
                "client_id": new_camera.client_id,
                "factory_id": new_camera.factory_id,
                "area_id": new_camera.area_id,
                "sub_area_id": new_camera.sub_area_id,
                "camera_id": new_camera.camera_id if new_camera and new_camera.camera_id else "",
                "camera_ip": new_camera.camera_ip if new_camera and new_camera.camera_ip else "",
                "camera_name": new_camera.camera_name if new_camera and new_camera.camera_name else "",
                "camera_position_no": new_camera.camera_position_no if new_camera and new_camera.camera_position_no else "",
                "nvr_no": new_camera.nvr_no if new_camera and new_camera.nvr_no else "",
                "username": new_camera.username if new_camera and new_camera.username else "",
                "password": new_camera.password if new_camera and new_camera.password else "",
                "stream": new_camera.stream if new_camera and new_camera.stream else "",
                "port": new_camera.port if new_camera and new_camera.port else "",
                "modules": Modules.get_records_by_list(verified_modules),  
                "active": new_camera.active
            }
        }
        

def get_cameras_old(data):
  
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    modules = data.get("modules", [])

    
    # Get active cameras for the specified client and factory
    query = Cameras.query.filter_by(
        client_id=client_id,
        factory_id=factory_id,
        active=True
    )
    
    # If modules are specified, filter cameras that have any of these modules
    if modules:
        query = query.filter(Cameras.modules.overlap(cast(modules, ARRAY(db.Integer))))
    
    cameras = query.all()
    
    # Format the camera data
    result = []
    for camera in cameras:
        result.append({
            "camera_id": camera.camera_id,
            "camera_name": camera.camera_name,
            "camera_ip": camera.camera_ip,
            "camera_position_no": camera.camera_position_no,
            "nvr_no": camera.nvr_no,
            "username": camera.username,
            "password": camera.password,
            "stream": camera.stream,
            "port": camera.port,
            "modules": camera.modules,
            "module_name": Modules.get_id_name_by_list(camera.modules),
            "area_id": camera.area_id,
            "sub_area_id": camera.sub_area_id,
            "active": camera.active
        })
    
    return result  # Always return the list, even if empty


def get_cameras(data):
    
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    modules = data.get("modules", [])
    page = int(data.get("page", 1))
    per_page = int(data.get("per_page", 20))
    window_time_minutes = data.get("window_time_minutes", 10)
    
    # Calculate cutoff time for active/inactive status
    cutoff_time = datetime.now() - timedelta(minutes=window_time_minutes)
    
    # Single comprehensive query with all counts
    cameras_query = (
        db.session.query(
            Cameras,
            CamerasliveFeed.timestamp,
            case(
                (CamerasliveFeed.timestamp >= cutoff_time, True),
                else_=False
            ).label('is_active')
        )
        .outerjoin(CamerasliveFeed, 
            and_(
                CamerasliveFeed.camera_id == Cameras.camera_id,
                CamerasliveFeed.client_id == client_id,
                CamerasliveFeed.factory_id == factory_id
            )
        )
        .filter(
            Cameras.client_id == client_id,
            Cameras.factory_id == factory_id,
            Cameras.active == True
        )
    )
    
    # Apply module filter if specified
    if modules:
        cameras_query = cameras_query.filter(Cameras.modules.overlap(cast(modules, ARRAY(db.Integer))))
    
    # Get all cameras data first (for counting)
    all_cameras_data = cameras_query.all()
    
    # Calculate counts from the fetched data
    total_records = len(all_cameras_data)
    active_cameras = sum(1 for _, _, is_active in all_cameras_data if is_active)
    inactive_cameras = total_records - active_cameras
    total_pages = (total_records + per_page - 1) // per_page
    
    # Apply pagination to the already fetched data
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_cameras_data = all_cameras_data[start_idx:end_idx]
    
    # Format the camera data
    cameras_list = []
    for camera_data in paginated_cameras_data:
        camera, timestamp, is_active = camera_data
        cameras_list.append({
            "camera_id": camera.camera_id,
            "camera_name": camera.camera_name,
            "camera_ip": camera.camera_ip,
            "camera_position_no": camera.camera_position_no,
            "nvr_no": camera.nvr_no,
            "username": camera.username,
            "password": camera.password,
            "stream": camera.stream,
            "port": camera.port,
            "modules": Modules.get_id_name_by_list(camera.modules),
            "area_id": camera.area_id,
            "sub_area_id": camera.sub_area_id,
            "active": camera.active,
            "is_live": bool(is_active),
            "last_active": timestamp.strftime("%d %b, %Y %I:%M %p") if timestamp else ""
        })
    
    return {
        "total_records": total_records,
        "total_pages": total_pages,
        "total_cameras": total_records,
        "active_cameras": active_cameras,
        "inactive_cameras": inactive_cameras,
        "cameras": cameras_list
    }


def check_cameras_status(data):
    """
    Check cameras status with active/inactive based on CamerasliveFeed table - Optimized
    """
    try:
        
        # Extract parameters from data
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        window_time_minutes = data.get("window_time_minutes", 10)

        # Single query to check both client and factory existence
        valid, error_response = validate_client_factory(client_id, factory_id)
        if not valid:
            return error_response

        # Calculate cutoff time once 
        cutoff_time = datetime.now() - timedelta(minutes=window_time_minutes)

        query = (
            db.session.query(
                Cameras.camera_id,
                Cameras.camera_name,
                Cameras.camera_ip,
                CamerasliveFeed.timestamp,
                CamerasliveFeed.image_url,
                case(
                    (CamerasliveFeed.timestamp >= cutoff_time, True),
                    else_=False
                ).label('is_active')
            )
            .outerjoin(CamerasliveFeed, 
                and_(
                    CamerasliveFeed.camera_id == Cameras.camera_id,
                    CamerasliveFeed.client_id == client_id,
                    CamerasliveFeed.factory_id == factory_id
                )
            )
            .filter(
                Cameras.client_id == client_id,
                Cameras.factory_id == factory_id,
                Cameras.active == True
            )
        ).all()

        # Count active cameras
        active_count = (
            db.session.query(
                func.count(Cameras.camera_id)
            )
            .outerjoin(CamerasliveFeed, 
                and_(
                    CamerasliveFeed.camera_id == Cameras.camera_id,
                    CamerasliveFeed.client_id == client_id,
                    CamerasliveFeed.factory_id == factory_id
                )
            )
            .filter(
                Cameras.client_id == client_id,
                Cameras.factory_id == factory_id,
                Cameras.active == True,
                CamerasliveFeed.timestamp >= cutoff_time
            )
        ).scalar() or 0

        total_cameras = len(query)
        cameras_list = []

        client_timezone = Client.get_timezone_by_id(client_id)   # fetch client
        client_tz = ZoneInfo(client_timezone)  # e.g. "Asia/Karachi"

        # Optimized loop - no calculations inside
        for row in query:
            camera_id, camera_name, camera_ip, timestamp, image_url, is_active = row
            
            local_timestamp = (
                timestamp.replace(tzinfo=timezone.utc).astimezone(client_tz)
                if timestamp else None
            )
            cameras_list.append({
                "camera_id": camera_id,
                "camera_name": camera_name,
                "camera_ip": camera_ip,
                "image_url": image_url or "",
                "active": bool(is_active),
                "last_active": (
                    local_timestamp.strftime("%d %b, %Y %I:%M %p") if local_timestamp else ""
                ),
            })

        return {
            "message": "Camera status fetched successfully",
            "success": True,
            "data": {
                "total_cameras": total_cameras,
                "active_cameras": active_count,
                "cameras_list": cameras_list
            }
        }

    except Exception as e:
        print(f"Error in check_cameras_status: {e}")
        return {
            "message": f"Error fetching camera status: {str(e)}",
            "success": False,
            "data": None
        }
    

def get_videos_summary(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        
        client = Client.get_by_id(client_id)
        factory = Factory.get_by_id(factory_id) 

        if not client or not factory:
            return {"message": "Client or Factory not found", "success": False, "data": None}

        videos = AstVideos.get_records_by_client_factory(client_id, factory_id)

        results = [
        {
            "id": record.id,
            "camera_id": record.camera_id,
            "name": record.name,
            "description": record.description,
            "video_url": record.video_url,
            "duration": record.duration,
            "size": record.size,
            "status": record.status,
            "created_at": record.created_at.strftime("%Y-%m-%d"),
            "updated_at": record.updated_at.strftime("%Y-%m-%d")
        } for record in videos
        ] 
        
        return results

    except Exception as e:
        print(f"Error in get_videos_summary: {e}")
        return {"error": str(e)}


def upload_video_controller(data, file):
    """Complete video upload business logic with all functionality in one function"""
    try:
        # Get upload folder from config
        upload_folder = app.config["uploads"]
        
        # 1. File validations
        if not file or file.filename == "":
            return {"error": "No file selected"}, 400
        
        if not allowed_video_file(file.filename):
            return {"error": f"Invalid video file. Allowed types are {list(ALLOWED_VIDEO_EXTENSIONS)}"}, 400
        
        # Handle modules - Simple comma-separated string
        modules_str = data.get("modules", "")
        if not modules_str:
            return {"error": "Modules are required"}, 400
            
        modules = [int(x.strip()) for x in modules_str.split(',') if x.strip().isdigit()]
        if not modules:
            return {"error": "Valid modules are required"}, 400
        
        # Validate modules exist in database
        # verified_modules = Modules.get_ids_by_list(modules)
        # if len(verified_modules) == 0:
        #     return {"error": "Invalid modules provided"}, 400
        
        # 2. Required fields validation
        required_fields = ["client_id", "factory_id", "camera_id", "name", "duration", "size", "modules", "status"]
        missing_fields = [field for field in required_fields if field not in data or not data[field]]
        if missing_fields:
            return {"error": f"Missing required fields: {', '.join(missing_fields)}"}, 400
        
        # 3. Database validations - Single query to check all entities
        validation_query = db.session.query(Client, Factory, Cameras).join(
            Factory, Factory.client_id == Client.client_id
        ).join(
            Cameras, and_(
                Cameras.client_id == Client.client_id,
                Cameras.factory_id == Factory.factory_id
            )
        ).filter(
            Client.client_id == data["client_id"],
            Factory.factory_id == data["factory_id"],
            Cameras.camera_id == data["camera_id"]
        ).first()
        
        if not validation_query:
            return {"error": "Client, Factory, or Camera does not exist"}, 400
            
        get_client, get_factory, get_camera = validation_query
        
        # 4. Create secure filename with timestamp
        filename = f"{datetime.now().strftime('%Y_%m_%d_%H_%M_%S')}_{secure_filename(file.filename)}"
        
        # 5. Create directory structure
        directory = os.path.join(upload_folder, f"{get_client.name}", f"{get_factory.name}", f"{get_camera.camera_id}")
        os.makedirs(directory, exist_ok=True)
        
        video_path = os.path.join(directory, filename)
        
        # 6. Save file to disk
        file.save(video_path)
        
        # 7. Get actual file size after saving
        actual_file_size = os.path.getsize(video_path)

        # Calculate SHA256SUM
        sha256sum = ""
        with open(video_path, "rb") as f:
            sha256sum = hashlib.sha256(f.read()).hexdigest()

        # Check for duplicate video
        if AstVideos.query.filter_by(sha256sum=sha256sum).first():
            os.remove(video_path)
            return {"error": "Duplicate video detected."}, 400
        
        # 8. Prepare data for database
        video_data = {
            "client_id": data["client_id"],
            "factory_id": data["factory_id"],
            "camera_id": data["camera_id"],
            "name": data["name"],
            "description": data.get("description", ""),
            "video_url": video_path.replace("\\", "/"),  # Store relative path
            "duration": int(data["duration"]),  
            "size": data["size"], 
            "modules": modules, 
            "status": data["status"],
            "sha256sum": sha256sum
        }
        
        # 9. Save to database
        video_record = AstVideos.create(video_data)
        
        # 10. Return success response
        return {
            "success": True,
            "message": "Video uploaded successfully",
            "data": {  
                "id": video_record.id,
                "video_url": video_record.video_url,
                "filename": filename,
                "path": video_path.replace("\\", "/"),
                "size": video_record.size,
                "modules": Modules.get_id_name_by_list(video_record.modules),
                "duration": video_record.duration,
                "status": video_record.status,
                "sha256sum": video_record.sha256sum
            }
        }, 200

    except Exception as e:
        # Clean up file if database operation fails
        if 'video_path' in locals() and os.path.exists(video_path):
            os.remove(video_path)
        return {"error": f"Error uploading video: {str(e)}"}, 500 


def get_factory_cameras(client_id, factory_id):
    try:
        cameras = Cameras.get_records_by_client_factory_id(client_id, factory_id)

        results = [
        {
            "camera_id": record.camera_id,
            "camera_name": record.camera_name,
            "camera_ip": record.camera_ip,
            "modules": record.modules,
            "active": record.active
        } for record in cameras
        ] 
        
        return results

    except Exception as e:
        print(f"Error in get_factory_cameras: {e}")
        return {"error": str(e)}

