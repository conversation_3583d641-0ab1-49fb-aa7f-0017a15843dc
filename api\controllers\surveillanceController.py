from sqlalchemy import String, and_, func, or_

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.surveillanceModels import *

from api import db
from sqlalchemy.sql import cast
from sqlalchemy.types import String, JSON
from api.utils.utils import roles
from sqlalchemy.orm import joinedload,contains_eager
from sqlalchemy.dialects.postgresql import ARRAY 
from api.utils.utils import *
from datetime import datetime, timedelta, time, date
import calendar
from isoweek import Week

# ----------------------- Monitoring Feed  --------------------------------
def monitoring_feed(data):

    try:
       
        client_id = data.get('client_id')
        factory_id = data.get('factory_id')

        # Get latest staff status
        latest_staff_event = (
            SurveillanceStaffEvents.query
            .filter(
                SurveillanceStaffEvents.client_id == client_id,
                SurveillanceStaffEvents.factory_id == factory_id
            )
            .order_by(desc(SurveillanceStaffEvents.timestamp))
            .first()
        )

        # Get latest customer count
        latest_customer_count = (
            SurveillanceCustomerCounts.query
            .filter(
                SurveillanceCustomerCounts.client_id == client_id,
                SurveillanceCustomerCounts.factory_id == factory_id
            )
            .order_by(desc(SurveillanceCustomerCounts.timestamp))
            .first()
        )

        # Get latest live camera image
        live_camera_image = (
            SurveillanceLiveCameraImages.query
            .filter(
                SurveillanceLiveCameraImages.client_id == client_id,
                SurveillanceLiveCameraImages.factory_id == factory_id
            )
            .order_by(desc(SurveillanceLiveCameraImages.timestamp))
            .first()
        )


        result = {
            "staff_status": {
                "is_present": latest_staff_event.is_present if latest_staff_event else False,
                #"image_path": latest_staff_event.image_path if latest_staff_event else None,
                "timestamp": latest_staff_event.timestamp.strftime("%Y-%m-%d %H:%M:%S") if latest_staff_event else None
            },
            "customer_count": {
                "count": latest_customer_count.count if latest_customer_count else 0,
                #"image_path": latest_customer_count.image_path if latest_customer_count else None,
                "timestamp": latest_customer_count.timestamp.strftime("%Y-%m-%d %H:%M:%S") if latest_customer_count else None
            }
            ,
            "live_image": live_camera_image.image_url if live_camera_image else None
        }
        
        return result

    except Exception as e:
        print(f"Error in total_entry_exit_data: {e}")
        return {}

# -----------------------  Analytics Report --------------------------------

def analytics_report(data):
    try:
        client_id = data.get('client_id')
        factory_id = data.get('factory_id')
        filters = data.get('filters', {})
        
        # Get date range based on filter type (date/week/month)
        start_date, end_date = parse_filters(data)
        
        # Get customer counts based on filter type
        customer_counts_query = (
            db.session.query(
                func.date(SurveillanceCustomerCounts.timestamp).label('date'),
                # Get the latest count for each date
                SurveillanceCustomerCounts.count.label('count')
            )
            .filter(
                SurveillanceCustomerCounts.client_id == client_id,
                SurveillanceCustomerCounts.factory_id == factory_id,
                SurveillanceCustomerCounts.timestamp.between(start_date, end_date)
            )
            # Subquery to get latest record for each date
            .distinct(func.date(SurveillanceCustomerCounts.timestamp))
            .order_by(
                func.date(SurveillanceCustomerCounts.timestamp),
                SurveillanceCustomerCounts.timestamp.desc()
            )
        )

        daily_counts = {
            record.date: record.count 
            for record in customer_counts_query.all()
        }

        # Calculate total customers by summing latest daily counts
        total_customers = sum(daily_counts.values())

        # Initialize absence tracking
        total_absence_minutes = 0  # Track in minutes for better precision
        absence_periods = []  # Track individual absence periods
        total_shift_hours = 0

        # Process each day in the date range
        current_date = start_date.date()
        while current_date <= end_date.date():
            # Get shift timings for the day
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            shift_start, shift_end = adjust_shift_times(day_start, day_end)
            print("Start and end time after shift adjustment: ",shift_start, shift_end)
            # Add shift duration to total hours
            shift_duration = (shift_end - shift_start).total_seconds() / 3600
            total_shift_hours += shift_duration

            # Get staff events for the current day
            day_events = (
                db.session.query(
                    SurveillanceStaffEvents.timestamp,
                    SurveillanceStaffEvents.is_present
                )
                .filter(
                    SurveillanceStaffEvents.client_id == client_id,
                    SurveillanceStaffEvents.factory_id == factory_id,
                    SurveillanceStaffEvents.timestamp.between(shift_start, shift_end)
                )
                .order_by(SurveillanceStaffEvents.timestamp)
                .all()
            )

            if not day_events:
                # No events for the day - count entire shift as absence
                absence_duration_minutes = shift_duration * 60  # Convert hours to minutes
                total_absence_minutes += absence_duration_minutes
                hours = int(absence_duration_minutes // 60)
                minutes = int(absence_duration_minutes % 60)
                absence_periods.append({
                    "time": f"{shift_start.strftime('%H:%M')} - {shift_end.strftime('%H:%M')}",
                    "duration": f"{hours}h {minutes}m"
                })
            else:
                last_time = shift_start
                last_present = False  # Assume absent at start of shift

                # Process each presence/absence event
                for event in day_events:
                    if not last_present:  # If was absent
                        absence_duration_minutes = (event.timestamp - last_time).total_seconds() / 60
                        if absence_duration_minutes > 0:  # Only add if there was actual absence
                            total_absence_minutes += absence_duration_minutes
                            hours = f"{absence_duration_minutes // 60:.2f}"
                            minutes = f"{absence_duration_minutes % 60:.2f}"
                            
                            absence_periods.append({
                                "time": f"{last_time.strftime('%H:%M')} - {event.timestamp.strftime('%H:%M')}",
                                "duration": f"{hours}h {minutes}m"
                            })
                    
                    last_time = event.timestamp
                    last_present = event.is_present

                # Handle time after last event until end of shift
                if not last_present:  # If last status was absent
                    absence_duration_minutes = (shift_end - last_time).total_seconds() / 60
                    if absence_duration_minutes > 0:
                        total_absence_minutes += absence_duration_minutes
                        hours = int(absence_duration_minutes // 60)
                        minutes = int(absence_duration_minutes % 60)
                        
                        absence_periods.append({
                            "time": f"{last_time.strftime('%H:%M')} - {shift_end.strftime('%H:%M')}",
                            "duration": f"{hours}h {minutes}m"
                        })

            current_date += timedelta(days=1)

        # Calculate total absence hours from accumulated minutes
        total_absence_hours = total_absence_minutes / 60

        # Calculate percentages
        absence_percentage = (total_absence_hours / total_shift_hours * 100) if total_shift_hours > 0 else 0
        present_percentage = 100 - absence_percentage
        
        # Format total absence consistently
        hours = int(total_absence_minutes // 60)
        minutes = int(total_absence_minutes % 60)
        formatted_absence = f"{hours}h {minutes}m"

        return {
            "total_customers": total_customers,
            "total_staff_absence": formatted_absence,
            "absence_percentage": round(absence_percentage, 2),
            "present_percentage": round(present_percentage, 2),
            "absence_breakdown": absence_periods,
            "period": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            }
        }

    except Exception as e:
        print(f"Error in analytics_report: {e}")
        return {}

def get_hourly_customer_traffic(data):
    try:
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        filters = data.get("filters", {})
        
        # Determine period type and value
        period_info = {
            "type": None,
            "value": None
        }
        
        if filters.get("date"):
            period_info["type"] = "day"
            period_info["value"] = filters["date"]
        elif filters.get("week"):
            period_info["type"] = "week"
            period_info["value"] = filters["week"]
        elif filters.get("month"):
            period_info["type"] = "month"
            period_info["value"] = filters["month"]
        else:
            today = datetime.now().strftime("%Y-%m-%d")
            period_info["type"] = "day"
            period_info["value"] = today
        
        start_date, end_date = parse_filters(data)
        traffic_data = []
        current_date = start_date.date()
        
        while current_date <= end_date.date():
            if period_info["type"] == "day":
                # Get shift timings for the day
                day_start = datetime.combine(current_date, datetime.min.time())
                day_end = datetime.combine(current_date, datetime.max.time())
                shift_start, shift_end = adjust_shift_times(day_start, day_end)
                
                start_hour = shift_start.hour
                end_hour = shift_end.hour
                
                hourly_counts = {}
                for hour in range(start_hour, end_hour + 1):
                    hour_start = datetime.combine(current_date, time(hour, 0))
                    hour_end = datetime.combine(current_date, time(hour, 59, 59))
                    
                    last_count = (
                        db.session.query(SurveillanceCustomerCounts.count)
                        .filter(
                            SurveillanceCustomerCounts.client_id == client_id,
                            SurveillanceCustomerCounts.factory_id == factory_id,
                            SurveillanceCustomerCounts.timestamp.between(hour_start, hour_end)
                        )
                        .order_by(SurveillanceCustomerCounts.timestamp.desc())
                        .first()
                    )
                    
                    if last_count:
                        hourly_counts[hour_start] = last_count[0]
                
                if hourly_counts:
                    total_customers = 0
                    previous_count = None
                    
                    for hour_start in sorted(hourly_counts.keys()):
                        current_count = hourly_counts[hour_start]
                        
                        if previous_count is not None:
                            hourly_difference = current_count - previous_count
                            total_customers += max(0, hourly_difference)
                            
                            traffic_data.append({
                                "hour": hour_start.strftime("%H:00"),
                                "count": max(0, hourly_difference),
                                "total_count": current_count
                            })
                        else:
                            total_customers += current_count
                            traffic_data.append({
                                "hour": hour_start.strftime("%H:00"),
                                "count": current_count,
                                "total_count": current_count
                            })
                        
                        previous_count = current_count
            else:
                # For week and month views, get daily totals
                day_start = datetime.combine(current_date, time(0, 0))
                day_end = datetime.combine(current_date, time(23, 59, 59))
                
                last_count = (
                    db.session.query(SurveillanceCustomerCounts.count)
                    .filter(
                        SurveillanceCustomerCounts.client_id == client_id,
                        SurveillanceCustomerCounts.factory_id == factory_id,
                        SurveillanceCustomerCounts.timestamp.between(day_start, day_end)
                    )
                    .order_by(SurveillanceCustomerCounts.timestamp.desc())
                    .first()
                )
                
                if last_count:
                    # Get shift timings for the day
                    day_start = datetime.combine(current_date, datetime.min.time())
                    day_end = datetime.combine(current_date, datetime.max.time())
                    shift_start, shift_end = adjust_shift_times(day_start, day_end)
                    shift_time = f'{shift_start.strftime("%I:%M %p")} - {shift_end.strftime("%I:%M %p")}'
                    
                    traffic_data.append({
                        "date": current_date.strftime("%B %d, %Y"),
                        "count": last_count[0],
                        "day": current_date.strftime("%A") if period_info["type"] == "week" else current_date.strftime("%d"),
                        "time": shift_time
                    })

            current_date += timedelta(days=1)

        newArr = get_period_array(period_info["type"])
        
        # Initialize the single daily_data object
        daily_data = {
            "date": start_date.strftime("%B %d, %Y"),
            "time": f'{shift_start.strftime("%I:%M %p")} - {shift_end.strftime("%I:%M %p")}' if 'shift_start' in locals() else "",
            "traffic_data": []
        }

        # For day view
        if period_info["type"] == "day":
            existing_hours = set(entry["hour"] for entry in traffic_data)
            
            # Add any missing hours with zero counts
            for hour in newArr:
                if hour not in existing_hours:
                    traffic_data.append({
                        "hour": hour,
                        "count": 0,
                        "total_count": 0
                    })
            
            # Sort traffic_data by hour
            traffic_data.sort(key=lambda x: x["hour"])
        
        # For week/month view
        else:
            existing_days = set(entry.get("day", "") for entry in traffic_data)
            
            # Add missing days with zero counts
            for day in newArr:
                if day not in existing_days:
                    traffic_data.append({
                        "date": "",
                        "count": 0,
                        "day": day,
                        "time": ""
                    })
            
            # Sort by weekday or day number
            if period_info["type"] == "week":
                weekday_order = {
                    "Monday": 0, "Tuesday": 1, "Wednesday": 2,
                    "Thursday": 3, "Friday": 4, "Saturday": 5, "Sunday": 6
                }
                traffic_data.sort(key=lambda x: weekday_order.get(x.get("day", ""), 7))
            else:
                traffic_data.sort(key=lambda x: x.get("day", ""))

        daily_data["traffic_data"] = traffic_data

        print(f"{newArr}")
        return {
            "period": period_info,
            "daily_data": [daily_data]  # Return as single-item array to maintain compatibility
        }

    except Exception as e:
        print(f"Error in get_hourly_customer_traffic: {e}")
        return {}


def get_staff_presence_timeline(data):
    """
    Get staff presence timeline for a factory based on date, week, or month filter
    Supports multiple date formats including ISO weeks (YYYY-Www)
    
    Args:
        data (dict): {
            "factory_id": int,
            "filters": {
                "date": "YYYY-MM-DD" (optional),
                "week": "YYYY-Www" or "YYYY-MM-DD" (optional),
                "month": "YYYY-MM" or "YYYY-MM-DD" (optional)
            }
        }
    
    Returns:
        dict: Timeline data in standardized format
    """
    try:
        # Validate required fields
        if not data or "factory_id" not in data or not data["factory_id"]:
            return {"message": "factory_id is required", "status": "error"}, 400
        
        factory_id = data["factory_id"]
        filters = data.get("filters", {})
        
        # Define fixed working hours
        WORKING_HOURS = {
            "weekday": {"start": (8, 0), "end": (21, 0)},   # Mon-Fri 8:00-21:00
            "saturday": {"start": (10, 0), "end": (16, 0)}, # Sat 10:00-16:00
            "sunday": {"start": (10, 0), "end": (15, 0)}    # Sun 10:00-15:00
        }

        def get_shift_hours(date_obj):
            """Returns shift start and end times for a given date"""
            day_of_week = date_obj.weekday()

            if day_of_week < 5:  # Monday to Friday
                shift_type = "weekday"
            elif day_of_week == 5:  # Saturday
                shift_type = "saturday"
            else:  # Sunday
                shift_type = "sunday"

            start_hour, start_min = WORKING_HOURS[shift_type]["start"]
            end_hour, end_min = WORKING_HOURS[shift_type]["end"]

            shift_start = datetime.combine(date_obj, time(start_hour, start_min))
            shift_end = datetime.combine(date_obj, time(end_hour, end_min))

            return shift_start, shift_end, shift_type

        def generate_timeline_for_date(date_obj):
            """Generate timeline for a single date"""
            shift_start, shift_end, shift_type = get_shift_hours(date_obj)

            # Query database for events
            events = (
                db.session.query(
                    SurveillanceStaffEvents.timestamp,
                    SurveillanceStaffEvents.is_present
                )
                .filter(
                    SurveillanceStaffEvents.factory_id == factory_id,
                    SurveillanceStaffEvents.timestamp.between(shift_start, shift_end)
                )
                .order_by(SurveillanceStaffEvents.timestamp)
                .all()
            )

            timeline_entries = []
            
            if not events:
                # Mark entire shift as absent if no events
                duration = (shift_end - shift_start).total_seconds() / 60
                hours = int(duration // 60)
                minutes = int(duration % 60)
                time_str = f'{shift_start.strftime("%H:%M")} - {shift_end.strftime("%H:%M")} = '
                time_str += f'{hours}h {minutes:02d}m' if hours > 0 else f'{minutes}m'
                
                timeline_entries.append({
                    "time": time_str,
                    "status": "absent",
                    "duration": int(duration)
                })
            else:
                # Handle period from shift start to first event
                first_event = events[0]
                initial_duration = (first_event.timestamp - shift_start).total_seconds() / 60
                
                if initial_duration > 0:
                    hours = int(initial_duration // 60)
                    minutes = int(initial_duration % 60)
                    time_str = f'{shift_start.strftime("%H:%M")} - {first_event.timestamp.strftime("%H:%M")} = '
                    time_str += f'{hours}h {minutes:02d}m' if hours > 0 else f'{minutes}m'
                    
                    timeline_entries.append({
                        "time": time_str,
                        "status": "late",
                        "duration": int(initial_duration)
                    })

                for i in range(len(events)):
                    current_event = events[i]
                    next_event = events[i + 1] if i < len(events) - 1 else None

                    if next_event:
                        duration = (next_event.timestamp - current_event.timestamp).total_seconds() / 60
                        hours = int(duration // 60)
                        minutes = int(duration % 60)
                        time_str = f'{current_event.timestamp.strftime("%H:%M")} - {next_event.timestamp.strftime("%H:%M")} = '
                        time_str += f'{hours}h {minutes:02d}m' if hours > 0 else f'{minutes}m'

                        timeline_entries.append({
                            "time": time_str,
                            "status": "present" if current_event.is_present else "absent",
                            "duration": int(duration)
                        })
                    else:
                        final_duration = (shift_end - current_event.timestamp).total_seconds() / 60
                        if final_duration > 0:
                            hours = int(final_duration // 60)
                            minutes = int(final_duration % 60)
                            time_str = f'{current_event.timestamp.strftime("%H:%M")} - {shift_end.strftime("%H:%M")} = '
                            time_str += f'{hours}h {minutes:02d}m' if hours > 0 else f'{minutes}m'

                            timeline_entries.append({
                                "time": time_str,
                                "status": "present" if current_event.is_present else "absent",
                                "duration": int(final_duration)
                            })

            return {
                "date": date_obj.strftime("%Y-%m-%d"),
                "day_name": date_obj.strftime("%A"),
                "shift_type": shift_type,
                "shift_time": f'{shift_start.strftime("%H:%M")} - {shift_end.strftime("%H:%M")}',
                "total_hours": f"{(shift_end - shift_start).total_seconds() / 3600:.1f}h",
                "entries": timeline_entries
            }

        filter_count = sum(1 for k in ['date', 'week', 'month'] if k in filters and filters[k])
        if filter_count != 1:
            return {"message": "Exactly one filter (date/week/month) must be provided", "status": "error"}, 400

        if filters.get("date"):
            try:
                date_obj = datetime.strptime(filters["date"], "%Y-%m-%d").date()
                return {
                    "message": "Timeline retrieved successfully",
                    "data": {
                        "StaffPresenceTimeline": generate_timeline_for_date(date_obj),
                    },
                    "status": "success"
                }
            except ValueError:
                return {"message": "Invalid date format. Use YYYY-MM-DD", "status": "error"}, 400

        elif filters.get("week"):
            try:
                week_str = filters["week"].strip()
                if '-' in week_str and 'W' in week_str.upper():
                    year, week = map(int, week_str.upper().split('-W'))
                else:
                    date_obj = datetime.strptime(week_str, "%Y-%m-%d").date()
                    year, week = date_obj.isocalendar()[0], date_obj.isocalendar()[1]

                iso_monday = datetime.strptime(f'{year}-W{week}-1', "%G-W%V-%u").date()
                days = [iso_monday + timedelta(days=i) for i in range(7)]

                days_data = [generate_timeline_for_date(d) for d in days]

                return {
                    "message": "Weekly timeline retrieved successfully",
                    "data": {
                        "StaffPresenceTimeline": {
                            "period": "week",
                            "week_number": week,
                            "year": year,
                            "start_date": days[0].strftime("%Y-%m-%d"),
                            "end_date": days[-1].strftime("%Y-%m-%d"),
                            "days": days_data
                        }
                    },
                    "status": "success"
                }
            except Exception as e:
                return {"message": f"Invalid week format. Use YYYY-Www or YYYY-MM-DD. Error: {str(e)}", "status": "error"}, 400

        elif filters.get("month"):
            try:
                month_str = filters["month"].strip()
                if len(month_str) == 7 and month_str[4] == '-':
                    year, month = map(int, month_str.split('-'))
                else:
                    date_obj = datetime.strptime(month_str, "%Y-%m-%d").date()
                    year, month = date_obj.year, date_obj.month

                num_days = calendar.monthrange(year, month)[1]
                days_data = [generate_timeline_for_date(date(year, month, d)) for d in range(1, num_days + 1)]

                return {
                    "message": "Monthly timeline retrieved successfully",
                    "data": {
                        "StaffPresenceTimeline": {
                            "period": "month",
                            "year": year,
                            "month": month,
                            "month_name": date(year, month, 1).strftime("%B"),
                            "start_date": f"{year}-{month:02d}-01",
                            "end_date": f"{year}-{month:02d}-{num_days}",
                            "days": days_data
                        }
                    },
                    "status": "success"
                }
            except ValueError:
                return {"message": "Invalid month format. Use YYYY-MM or YYYY-MM-DD", "status": "error"}, 400

    except Exception as e:
        print(f"Error in get_staff_presence_timeline: {str(e)}")
        return {"message": f"Internal server error: {str(e)}", "status": "error"}, 500





def get_period_array(period_type, date=None):
    """
    Returns simple array based on period type
    
    Args:
        period_type (str): Type of period ('day', 'week', or 'month')
        date (datetime, optional): Date for month calculation. Defaults to current date.
    
    Returns:
        list: Array of hours or days based on period type
    """
    try:
        if period_type == "day":
            # Create 24-hour array ["00:00", "01:00", ... "23:00"]
            return [f"{hour:02d}:00" for hour in range(24)]

        elif period_type == "week":
            # Return weekday names
            return ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

        elif period_type == "month":
            if date is None:
                date = datetime.now()
            # Get last day of month
            _, last_day = calendar.monthrange(date.year, date.month)
            # Return array of days ["01", "02", ... "31"]
            return [f"{day:02d}" for day in range(1, last_day + 1)]

        else:
            return []

    except Exception as e:
        print(f"Error in get_period_array: {e}")
        return []
