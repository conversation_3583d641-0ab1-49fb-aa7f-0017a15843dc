from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.hseModels import *
from api.models.annotationModels import *
from api.models.uamModels import *

from api.payloads.annotationPayloads import *
from api import annotation_ns
from api.controllers import annotationController as annotate_func

from api import db, mqtt_client
from api.services import token_required, user_token_required
from sqlalchemy import and_, or_
import json


@annotation_ns.route("/get_unannotated_images")
@annotation_ns.doc("Get unannotated images")
class GetUnannotatedImages(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get unannotated images")
    @annotation_ns.expect(get_unannotated_images_payload)
    @annotation_ns.response(200, "Unannotated images fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["user_id", "client_id", "factory_id", "batch_id", "limit"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_unannotated_images(data)
            
            return {"message": "Unannotated images fetched successfully.", "annotated_images_count": results[1], "data": results[0]}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@annotation_ns.route("/submit_annotated_images")
@annotation_ns.doc("Submit annotated images")
class SubmitAnnotatedImages(Resource):
    #@user_token_required 
    @annotation_ns.doc("Submit annotated images")
    @annotation_ns.expect(submit_annotated_images_payload)
    @annotation_ns.response(200, "Submit annotated images successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def post(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["annotation_list"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            annotation_list = data.get("annotation_list")

            AnnotationCameraImages.update_annotation(annotation_list)
            
            return {"message": "Submit annotated images successfully."}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
@annotation_ns.route("/get_review_images")
@annotation_ns.doc("Get review images")
class GetReviewImages(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get review images")
    @annotation_ns.expect(get_review_images_payload)
    @annotation_ns.response(200, "Review images fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["user_id", "annotation_user_id", "client_id", "factory_id", "batch_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_review_images(data)
            
            return {"message": "Review images fetched successfully.", "reviewed_images_count": results[1],"data": results[0]}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@annotation_ns.route("/submit_review_images")
@annotation_ns.doc("Submit review images")
class SubmitReviewImages(Resource):
    #@user_token_required 
    @annotation_ns.doc("Submit review images")
    @annotation_ns.expect(submit_reviewed_images_payload)
    @annotation_ns.response(200, "Submit review images successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def post(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["reviewed_list"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            reviewed_list = data.get("reviewed_list")

            AnnotationCameraImages.update_review(reviewed_list)
            
            return {"message": "Submit review images successfully."}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@annotation_ns.route("/get_annotation_summary")
@annotation_ns.doc("Get annotation summary")
class GetAnnotationSummary(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get annotation summary")
    @annotation_ns.expect(get_annotation_summary_payload)
    @annotation_ns.response(200, "Annotation summary fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["user_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_annotation_summary(data)
            
            return {"message": "Annotation summary fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@annotation_ns.route("/get_dataset_summary")
@annotation_ns.doc("Get dataset summary")
class GetDatasetSummary(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get dataset summary")
    @annotation_ns.expect(get_dataset_summary_payload)
    @annotation_ns.response(200, "Dataset summary fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["user_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_dataset_summary(data)
            
            return {"message": "Dataset summary fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@annotation_ns.route("/generate_dataset")
@annotation_ns.doc("Generate Dataset for annotated images")
class GenerateDataset(Resource):
    #@user_token_required 
    @annotation_ns.doc("Generate Dataset for annotated images")
    @annotation_ns.expect(generate_dataset_payload)
    @annotation_ns.response(200, "Generate Dataset successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def post(self): 
        data = annotation_ns.payload
        try:
            req_fields = ["client_id","factory_id", "batch_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            success, message = annotate_func.generate_dataset(data)
            if success:
                return {"message": message}, 200
            else:
                return {"message": message}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@annotation_ns.route("/insert_unannotated_images")
@annotation_ns.doc("Insert Images")
class InsertImages(Resource):
    @annotation_ns.doc("Insert Images")
    @annotation_ns.expect(annotation_camera_images_payload_model)
    @annotation_ns.response(200, "Images insert successfully.")
    @annotation_ns.response(400, "Validation Error")
    def post(self):
        data = annotation_ns.payload
        try:
            req_fields = ["images"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            rows = data.get("images")
            any_rows_added = AnnotationCameraImages.add_multiple_rows(rows) 
                    
            # Generate the appropriate message
            if any_rows_added:
                message = "Images insert successfully."
            else:
                message = "No rows were added."
            
            response = {"message": message}  
                
            return response, 200 

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@annotation_ns.route("/delete_annotation_camera_image/<int:id>")
@annotation_ns.doc("Delete Camera Image")
class DeleteCameraImage(Resource):
    #@user_token_required
    @annotation_ns.doc("Delete Camera Image")
    @annotation_ns.response(200, "Camera image deleted successfully.")
    @annotation_ns.response(400, "Validation Error")
    @annotation_ns.doc(security=["Authorization", "Token-Type"])
    def delete(self, id):
        try:
            if not id:
                return {"message": "Image ID is missing."}, 400
 
            image = AnnotationCameraImages.get_by_id(id)
            if not image:
                return {"message": "Image does not exist."}, 400
            
            AnnotationCameraImages.delete(image)
            return {"message": "Image deleted successfully", "success": True}, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route('/add_yolo_classes')
@annotation_ns.doc('Add or Update Yolo Classes')
class AddYoloClasses(Resource):
    @annotation_ns.expect(yolo_classes_payload)
    @annotation_ns.response(200, 'Success')
    @annotation_ns.response(400, 'Validation Error')
    @annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        try:
            payload = annotation_ns.payload
            
            req_fields = ["client_id", "factory_id", "modules", "batch_id"]
            for field in req_fields:
                if not payload.get(field):
                    return {"message": f"{field} is missing."}, 400

            AnnotationYoloClasses.create_update(payload)

            return {"message": "Record created or updated successfully.", "success": True}, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error: {e}"})


@annotation_ns.route('/get_yolo_classes')
@annotation_ns.doc('Get Yolo Classes by Client and Factory ID')
class GetYoloClasses(Resource):
    #@user_token_required 
    @annotation_ns.expect(get_yolo_classes_payload)
    @annotation_ns.response(200, 'Success')
    @annotation_ns.response(400, 'Validation Error')
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        try:
            data = annotation_ns.payload

            req_fields = ["client_id", "factory_id", "batch_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
                    
            client_id = data.get('client_id')
            factory_id = data.get('factory_id')
            batch_id = data.get('batch_id')

            rec = AnnotationYoloClasses.get_record_by_client_factory_batch_id(client_id=client_id, factory_id=factory_id, batch_id=batch_id)
            
            if not rec:
                return {"message": "No records found.", "success": False}, 404

            client_name = Client.get_name_by_id(rec.client_id) if Client.get_name_by_id(rec.client_id) else ""
            factory_name = Factory.get_name_by_id(rec.factory_id) if Factory.get_name_by_id(rec.factory_id) else ""
            

            result = {
                "yolo_classes_id": rec.yolo_classes_id,
                "client_id": rec.client_id,
                "client_name": client_name,
                "factory_id": rec.factory_id,
                "factory_name": factory_name,
                "modules": Modules.get_id_name_by_list(rec.modules),
                "batch_id": rec.batch_id,
                "created_at": rec.created_at.strftime("%Y-%m-%d"),
                "updated_at": rec.updated_at.strftime("%Y-%m-%d")
            }

            return {"message": "Records fetched successfully.", "success": True, "data": result}, 200
        
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error: {e}"})


@annotation_ns.route('/get_all_yolo_classes')
@annotation_ns.doc('Get Yolo Classes by Client and Factory ID')
class GetAllYoloClasses(Resource):
    #@user_token_required 
    @annotation_ns.response(200, 'Success')
    @annotation_ns.response(400, 'Validation Error')
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        try:
            records = AnnotationYoloClasses.get_all_records()
            
            if not records:
                return {"message": "No records found.", "success": False}, 404

            result = []
            for rec in records:
                
                client_name = Client.get_name_by_id(rec.client_id) if Client.get_name_by_id(rec.client_id) else ""
                factory_name = Factory.get_name_by_id(rec.factory_id) if Factory.get_name_by_id(rec.factory_id) else ""

                result.append({
                    "yolo_classes_id": rec.yolo_classes_id,
                    "client_id": rec.client_id,
                    "client_name": client_name,
                    "factory_id": rec.factory_id,
                    "factory_name": factory_name,
                    "modules": Modules.get_id_name_by_list(rec.modules),
                    "batch_id": rec.batch_id,
                    "created_at": rec.created_at.strftime("%Y-%m-%d"),
                    "updated_at": rec.updated_at.strftime("%Y-%m-%d")
                })

            return {"message": "Records fetched successfully.", "success": True, "data": result}, 200
        
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error: {e}"})
            
            
@annotation_ns.route("/get_user_summary")
@annotation_ns.doc("Get User Summary")
class GetUserSummary(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get User Summary")
    @annotation_ns.expect(get_user_summary_payload)
    @annotation_ns.response(200, "User Summary fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data = annotation_ns.payload
        try:
            req_fields = ["user_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_user_summary(data)
            
            return {"message": "User Summary fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            

@annotation_ns.route("/get_user_monthly_chart")
@annotation_ns.doc("Get User Monthly Chart")
class GetUsertMonthlyChart(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get User Monthly Chart")
    @annotation_ns.expect(get_user_monthly_chart_payload)
    @annotation_ns.response(200, "User Monthly Chart fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        data = annotation_ns.payload
        try:
            req_fields = ["user_id"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            results = annotate_func.get_user_monthly_chart(data)
            
            return {"message": "User Monthly Chart fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@annotation_ns.route("/get_all_user_summary")
@annotation_ns.doc("Get All User Summary")
class GetAllUserSummary(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get All User Summary")
    @annotation_ns.response(200, "All User Summary fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):

        try:
            results = annotate_func.get_all_users_summary()
            
            return {"message": "All User Summary fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
            
@annotation_ns.route("/request_unannotated_images")
@annotation_ns.doc("Request Unannotated Images from AI")
class RequestUnannotatedImages(Resource):
    #@user_token_required 
    @annotation_ns.response(200, "Request Unannotated Images from AI successfully.")
    @annotation_ns.expect(request_unannotated_images_payload)
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        payload = annotation_ns.payload
        try:
            req_fields = ["client_id", "factory_id", "images_count", "camera_ids", "batch_id"]
            for field in req_fields:
                if not payload.get(field):
                    return {"message": f"{field} is missing."}, 400
                    
            images_count = payload.get("images_count", 0)
            if images_count > 1000:
                return {"message": f"images_count: {images_count} is too big, Limit is 1000."}, 400
            
            client_id = payload["client_id"]
            client_name = Client.get_name_by_id(client_id) if Client.get_name_by_id(client_id) else None
            if client_name is None:
                return {"message": f"Client ID: {client_id} not exist in database."}, 400
                
            factory_id = payload["factory_id"]
            factory_name = Factory.get_name_by_id(factory_id) if Factory.get_name_by_id(factory_id) else None
            if factory_name is None:
                return {"message": f"Factory ID: {factory_id} not exist in database."}, 400
            
            batch_id = payload.get("batch_id", 1)
            
            #Sending Add Camera data to AI machine 
            topic = f"{client_name}/{factory_name}/annotation/in"
            topic = topic.lower()
            data = {}
            data['command'] = "get_unannotated_images"
            data['images_count'] = images_count
            data['client_id'] = client_id
            data['factory_id'] = factory_id
            data['batch_id'] = batch_id
            data['skip_frames'] = payload.get("skip_frames", 0)
            data['background_images'] = payload.get("background_images", False)
            data['camera_ids'] = payload.get("camera_ids")  # Use payload directly here
            data['bucket_name'] = "disrupt-hse-images"
            folder = f"{client_name}/{factory_name}"
            data['folder'] = folder.lower()
            data['endpoint'] =  f"https://beannotation.disruptlabs.tech/annotation/insert_unannotated_images"

            data_list = []
            data_list.append(data)
            json_string = json.dumps(data_list)
            
            mqtt_client.publish(topic, json_string)
            
            return {"message": f"Request Unannotated Images from AI successfully on topic: {topic}.", "data": data_list}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@annotation_ns.route("/get_user_progress")
@annotation_ns.doc("Get all images for porgress")
class GetAllImages(Resource):
    @annotation_ns.expect(get_user_progress_payload)
    @annotation_ns.response(200, "Progress fetched successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            results = annotate_func.get_all_images(data)
            return {"message": "Progress fetched successfully.", "data": results}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route("/assign_annotation_job")
@annotation_ns.doc("Assign Annotation Job")
class AddAnnotationJob(Resource):
    @annotation_ns.expect(assign_annotation_job_payload)
    @annotation_ns.response(200, "Annotation Job assigned successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            AnnotationJobAssignment.create(data)
            return {"message": "Annotation Job assigned successfully."}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route("/transfer_annotation_job")
@annotation_ns.doc("Transfer Annotation Job")
class TransferAnnotationJob(Resource):
    @annotation_ns.expect(transfer_annotation_job_payload)
    @annotation_ns.response(200, "Annotation Job transferred successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            response, status = annotate_func.transfer_annotation_job(data)
            return response, status
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route("/get_all_annotation_jobs")
@annotation_ns.doc("Get All Annotation Jobs")
class GetAllAnnotationJobs(Resource):
    @annotation_ns.expect(get_all_annotation_jobs_payload)
    @annotation_ns.response(200, "Annotation Jobs fetched successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            results = annotate_func.get_all_annotation_jobs(data)
            return {"message": "Annotation Jobs fetched successfully.", "data": results}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@annotation_ns.route("/update_annotation_job_status")
@annotation_ns.doc("Update Annotation Job Status")
class UpdateAnnotationJobStatus(Resource):
    @annotation_ns.expect(update_annotation_job_status_payload)
    @annotation_ns.response(200, "Annotation Job Status updated successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            req_fields = ["job_id", "status"]
            for field in req_fields:
                if not data.get(field):
                    return {"message": f"{field} is missing."}, 400
            
            job_id = data["job_id"]
            status = data["status"].lower()
            
            job = AnnotationJobAssignment.get_record_by_id(job_id)
            if not job:
                return {"message": "Annotation Job does not exist"}, 400
            
            job.status = status
            db.session.commit()
            
            return {"message": "Annotation Job Status updated successfully."}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route("/delete_annotation_job/<int:id>")
@annotation_ns.doc("Delete Annotation Job")
class DeleteAnnotationJob(Resource):
    @annotation_ns.response(200, "Annotation Job deleted successfully.")
    def delete(self, id):
        try:
            if not id:
                return {"message": f"Job ID is missing."}, 400
            
            job = AnnotationJobAssignment.get_record_by_id(id)
            if not job:
                return {"message": "Annotation Job does not exist"}, 400
            
            AnnotationJobAssignment.delete(job)
            return {"message": "Annotation Job deleted successfully" , "success": True }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
                

@annotation_ns.route("/get_user_job_progress")
@annotation_ns.doc("Get User Job Progress")
class GetAllUserSummary(Resource):
    #@user_token_required 
    @annotation_ns.doc("Get User Job Progress ")
    @annotation_ns.response(200, "All User Summary fetched successfully.")
    #@annotation_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):

        try:
            results = annotate_func.get_user_job_progress()
            
            return {"message": "User Job Progress fetched successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
       
            
@annotation_ns.route("/get_training_image_counts_per_camera")
@annotation_ns.doc("Get Training Image Counts per Camera")
class GetCameraImageCountsByDataset(Resource):
    @annotation_ns.expect(get_training_image_counts_per_camera_payload) 
    @annotation_ns.response(200, "Camera image counts fetched successfully.")
    def put(self):
        data = annotation_ns.payload
        try:
            results = annotate_func.get_training_image_counts_per_camera(data)
            return {"message": "Camera image counts fetched successfully.", "data": results}, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@annotation_ns.route("/insert_false_alert_images")
@annotation_ns.doc("Insert False Alert Images")
class InsertImages(Resource):
    @annotation_ns.doc("Insert False Alert Images")
    @annotation_ns.expect(insert_false_alert_images_payload_model)
    @annotation_ns.response(200, "Images insert successfully.")
    @annotation_ns.response(400, "Validation Error")
    def post(self):
        data = annotation_ns.payload
        try:
            response, status = annotate_func.insert_false_alert_images(data)
            return response, status
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})