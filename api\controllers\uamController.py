from flask import json
from copy import deepcopy
import datetime

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.reportModels import *
from sqlalchemy.orm import aliased
from sqlalchemy import String, and_, case, func
from api import db, app

from sqlalchemy.exc import SQLAlchemyError

def add_user(data):
    # Validate factory
    factory_id = data.get('factory_id')
    role_id = data.get('role_id')
    client_id = data.get('client_id')

    # # Check if user already exists
    get_user = Users.get_by_email(data["email"].strip())

    if get_user:
        return {"message": "User with this email exists."}, 400

    # Check if factory exists
    valid_factory = Factory.get_by_id(factory_id)
    if not valid_factory:
        return {"message": "Factory does not exist", "status": False}
    
    # Check if role exists
    valid_role = Role.get_by_id(role_id)
    if not valid_role:
        return {"message": "Role does not exist", "status": False}
    
    # Check if client exists
    valid_client = Client.get_by_id(client_id)
    if not valid_client:
        return {"message": "Client does not exist", "status": False}
    
    # if role.role_name.lower() != 'global' and len(valid_area_ids) == 0:
    #     return {"message": "One or more Area IDs do not exist","status": False}
    
    # if role.role_name.lower() == 'area':
    #     assigned_areas=[] 
    #     for area_id in valid_area_ids:
    #         if UserArea.get_by_area_id(area_id):
                
    #             return {"message": "This area is already assigned to a user.", "status": False}
    
    # Email notifications processing
    email_notif = data.get("email_notifications", {})
    if email_notif.get("toggle", False):
        email_areas_valid = Area.get_ids_by_list(email_notif.get("areas", []))
        email_valid_sub_areas = SubArea.get_ids_by_list(email_notif.get("sub_areas", []))
        if len(email_areas_valid) == 0 or len(email_valid_sub_areas) == 0 or len(email_notif.get("alert_emails", [])) == 0 :
            return {"message": "Invalid email notification area IDs or sub area IDs or alert emails provided.","status": False}
    
    # WhatsApp notifications processing
    whatsapp_notif = data.get("whatsapp_notifications", {})
    if whatsapp_notif.get("toggle", False):
        whatsapp_areas_valid = Area.get_ids_by_list(whatsapp_notif.get("areas", []))
        whatsapp_valid_sub_areas = SubArea.get_ids_by_list(whatsapp_notif.get("sub_areas", []))
        if len(whatsapp_areas_valid) == 0 or len(whatsapp_valid_sub_areas) == 0 or len(whatsapp_notif.get("phone_numbers", [])) == 0:
            return {"message": "Invalid WhatsApp notification area IDs or sub area IDs or phone numbers provided.","status": False}     


    try:
        # Add new user
        new_user = Users.create_user(data)
        
        # Add user Role
        user_role = UserRole.create(new_user.user_id, valid_role.role_id)

        # Add user factory
        user_factory= UserFactory.create(new_user.user_id, valid_factory.factory_id)
        # Add user areas
        # user_areas= UserArea.create_multiple(new_user.user_id,valid_area_ids)
    
        # Add Whatsapp Notifications settings
        if whatsapp_notif.get("toggle", False):
            whatsapp_payload={
                "phone_numbers": whatsapp_notif["phone_numbers"],
                "areas":whatsapp_areas_valid,
                "sub_areas": whatsapp_valid_sub_areas
            }
            WhatsAppNotification.add_whatsapp_notification(new_user.user_id, whatsapp_payload)
            
        # Add Email Notifications settings
        if email_notif.get("toggle", False):
            email_payload={
                "alert_emails": email_notif["alert_emails"],
                "areas":email_areas_valid,
                "sub_areas": email_valid_sub_areas
            }
            EmailNotification.add_email_notification(new_user.user_id, email_payload)
        
        db.session.commit()

        # it_user=Users.get_by_user_id(data["user_id"])
        email_notification_settings= {
            "toggle": True,
            "areas": [{"id": area.area_id,"name": area.name, } for area in Area.get_records_by_list(email_areas_valid)],
            "sub_areas": [{"id": subarea.sub_area_id,"name": subarea.name, } for subarea in SubArea.get_records_by_list(email_valid_sub_areas)],
            "id": new_user.email_notifications[0].id, 
            "alert_emails":  new_user.email_notifications[0].alert_emails["alert_emails"] if  new_user.email_notifications[0].alert_emails else [],
            } \
            if new_user.email_notifications else {"toggle": False}
        
        whatsapp_notification_settings= {
            "toggle": True,
            "areas": [{"id": area.area_id,"name": area.name, } for area in Area.get_records_by_list(whatsapp_areas_valid)],
            "sub_areas": [{"id": subarea.sub_area_id,"name": subarea.name, } for subarea in SubArea.get_records_by_list(whatsapp_valid_sub_areas)],
            "id": new_user.whatsapp_notifications[0].id, 
            "phone_numbers":  new_user.whatsapp_notifications[0].phone_numbers["phone_numbers"] if new_user.whatsapp_notifications[0].phone_numbers else [],
            } \
            if new_user.whatsapp_notifications else {"toggle": False}

        response_data = {
                    "user_id": new_user.user_id,
                    "name": new_user.name,
                    "email": new_user.email,
                    "mobile_no": new_user.mobile_no,
                    # "created_by": it_user.name if it_user else "" ,
                    # "created_by_role": it_user.roles.role.role_name if it_user else "" ,
                    "role_id": user_role.role_id,
                    "role_name": user_role.role.role_name,
                    # "factories":[{ "id": i.factory.factory_id, "name": i.factory.name } for i in new_user.factories if i.factory.active],
                    "factory_id": valid_factory.factory_id,
                    "factory_name": valid_factory.name if valid_factory else "",
                    # "areas":  [{"id": i.area.area_id,"name": i.area.name, } for i in new_user.areas if i.area.active ],
                    "email_notifications": email_notification_settings,
                    "whatsapp_notifications": whatsapp_notification_settings,
                    "status": True if new_user.active else False,
                    "created_at": new_user.created_at.strftime("%Y-%m-%d %I:%M %p"),
                    "updated_at": new_user.updated_at.strftime("%Y-%m-%d %I:%M %p") if new_user.updated_at else "", 
                }

        return {"message": "User added successfully","status": True, "data": response_data} 
    
    except (SQLAlchemyError, Exception) as e:
        db.session.rollback()
        return {"message": f"An error occurred: {str(e)}", "status": False}    
    
    
def update_user(get_user,data,role):
    
    valid_factory_ids = Factory.get_ids_by_list(data.get('factories', []))
    valid_area_ids = Area.get_ids_by_list(data.get('areas', []))
    
    if len(valid_factory_ids) == 0:
            return {"message": "One or more Factory IDs do not exist","status": False}
    
    if role.role_name.lower() != 'global' and len(valid_area_ids) == 0:
        return {"message": "One or more Area IDs do not exist","status": False}
    
    # Email notifications processing
    email_notif = data.get("email_notifications", {})
    if email_notif.get("toggle", False):
        email_areas_valid = Area.get_ids_by_list(email_notif.get("areas", []))
        email_valid_sub_areas = SubArea.get_ids_by_list(email_notif.get("sub_areas", []))
        if len(email_areas_valid) == 0 or len(email_valid_sub_areas) == 0 or len(email_notif.get("alert_emails", [])) == 0 :
            return {"message": "Invalid email notification area IDs or sub area IDs or alert emails provided.","status": False}
    
    # WhatsApp notifications processing
    whatsapp_notif = data.get("whatsapp_notifications", {})
    if whatsapp_notif.get("toggle", False):
        whatsapp_areas_valid = Area.get_ids_by_list(whatsapp_notif.get("areas", []))
        whatsapp_valid_sub_areas = SubArea.get_ids_by_list(whatsapp_notif.get("sub_areas", []))
        if len(whatsapp_areas_valid) == 0 or len(whatsapp_valid_sub_areas) == 0 or len(whatsapp_notif.get("phone_numbers", [])) == 0:
            return {"message": "Invalid WhatsApp notification area IDs or sub area IDs or phone numbers provided.","status": False}     

    print("Checks Done")
    # update user
    resp = Users.update(get_user, data)
    # Update user Role
    user_role = UserRole.get_by_user_id(resp.user_id)
    if user_role:
        updated_user_role= UserRole.update(user_role,resp.user_id, role.role_id)

    # Update user factories
    update_user_factory= UserFactory.update_user_factories(resp.user_id,valid_factory_ids)
    # Update user areas
    update_user_areas= UserArea.update_user_areas(resp.user_id,valid_area_ids)
    
     # Update Whatsaopp Notifications settings
    if whatsapp_notif.get("toggle", False):
        whatsapp_payload={
            "phone_numbers": whatsapp_notif["phone_numbers"],
            "areas":whatsapp_areas_valid,
            "sub_areas": whatsapp_valid_sub_areas
        }
        WhatsAppNotification.update_whatsapp_notification(resp.user_id, whatsapp_payload)
    else:
        WhatsAppNotification.deactivate_whatsapp_notification(resp.user_id)
    
    # Update Email Notifications settings 
    if email_notif.get("toggle", False):
        email_payload={
            "alert_emails": email_notif["alert_emails"],
            "areas":email_areas_valid,
            "sub_areas": email_valid_sub_areas
        }
        EmailNotification.update_email_notification(resp.user_id, email_payload)
    else:
        EmailNotification.deactivate_email_notification(resp.user_id)
   
    print("update done")
    # it_user=Users.get_by_user_id(data["admin_id"])
    email_notification_settings= {
        "toggle": True,
         "areas": [{"id": area.area_id,"name": area.name, } for area in Area.get_records_by_list(email_areas_valid)],
          "sub_areas": [{"id": subarea.sub_area_id,"name": subarea.name, } for subarea in SubArea.get_records_by_list(email_valid_sub_areas)],
          "id": resp.email_notifications[0].id, 
          "alert_emails":  resp.email_notifications[0].alert_emails["alert_emails"] if  resp.email_notifications[0].alert_emails else [],
        } \
        if email_notif.get("toggle", False) else {"toggle": False, "areas": [], "sub_areas":[], "alert_emails":[]}
        
    whatsapp_notification_settings= {
        "toggle": True,
         "areas": [{"id": area.area_id,"name": area.name, } for area in Area.get_records_by_list(whatsapp_areas_valid)],
          "sub_areas": [{"id": subarea.sub_area_id,"name": subarea.name, } for subarea in SubArea.get_records_by_list(whatsapp_valid_sub_areas)],
          "id": resp.whatsapp_notifications[0].id, 
          "phone_numbers":  resp.whatsapp_notifications[0].phone_numbers["phone_numbers"] if resp.whatsapp_notifications[0].phone_numbers else [],
        } \
        if whatsapp_notif.get("toggle", False) else {"toggle": False, "areas": [], "sub_areas":[], "phone_numbers":[]}
   
    response_data = {
                "user_id": resp.user_id,
                "name": resp.name,
                "email": resp.email,
                "mobile_no": resp.mobile_no, 
                "role_name": user_role.role.role_name,
                "role_id": user_role.role.role_id,
                "factories":[{ "id": i.factory.factory_id, "name": i.factory.name } for i in resp.factories if i.factory.active],
                "areas":  [{"id": i.area.area_id,"name": i.area.name, } for i in resp.areas if i.area.active ],
                "email_notifications":  email_notification_settings ,
                "whatsapp_notifications":whatsapp_notification_settings ,
                "status": True if resp.active else False,
                "created_at": resp.created_at.strftime("%Y-%m-%d %I:%M %p"),
                "updated_at": resp.updated_at.strftime("%Y-%m-%d %I:%M %p") if resp.updated_at else "", 
            }

    return {"message": "User added successfully","status": True, "data": response_data}     

def get_all_user():
    
    AreaAlias = aliased(Area)
    SubAreaAlias = aliased(SubArea)
    
    email_notifications_query = (
    db.session.query(
        Users.user_id,
        EmailNotification.alert_emails,
        EmailNotification.id,
        func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                  "area_id", AreaAlias.area_id,
                "area_name", AreaAlias.name,
                )
            )
        )
        .filter(AreaAlias.area_id != None)  # Ensure no null areas
        .label("email_areas"),
        
        # Aggregating sub-areas as JSON objects, with filter to ensure non-null sub-areas
         func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                 "sub_area_id", SubAreaAlias.sub_area_id,
                "sub_area_name", SubAreaAlias.name
                )
            )
        )
        .filter(SubAreaAlias.sub_area_id != None)  # Ensure no null sub-areas
        .label("email_sub_areas")
    )
    .outerjoin(EmailNotification, EmailNotification.user_id == Users.user_id)
    .outerjoin(AreaAlias, and_(
        func.array_length(EmailNotification.areas, 1) > 0,
        AreaAlias.area_id == func.any(EmailNotification.areas),
        AreaAlias.active == True  # Ensures only active areas
    ))
    .outerjoin(SubAreaAlias, and_(
        func.array_length(EmailNotification.sub_areas, 1) > 0,
        SubAreaAlias.sub_area_id == func.any(EmailNotification.sub_areas), 
        SubAreaAlias.active == True,  # Ensures only active sub_areas
        SubAreaAlias.area_id == AreaAlias.area_id  # Match sub_areas with the correct area
    ))
    #.filter(Users.active == True,EmailNotification.active)  # Only active users
    .group_by(Users.user_id, EmailNotification.alert_emails, EmailNotification.id)
    .subquery()
)
    
    whatsapp_notifications_query = (
    db.session.query(
        Users.user_id,
        WhatsAppNotification.phone_numbers,
        WhatsAppNotification.id,
        
        # Aggregating areas as JSON objects with filter to exclude empty/None values
        func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                  "area_id", AreaAlias.area_id,
                "area_name", AreaAlias.name,
                )
            )
        )
        .filter(AreaAlias.area_id != None)  # Exclude null areas
        .label("whatsapp_areas"),

        # Aggregating sub-areas as JSON objects with filter to exclude empty/None values
         func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                 "sub_area_id", SubAreaAlias.sub_area_id,
                "sub_area_name", SubAreaAlias.name
                )
            )
        )
        .filter(SubAreaAlias.sub_area_id != None)  # Exclude null sub-areas
        .label("whatsapp_sub_areas")
    )
    .outerjoin(WhatsAppNotification, WhatsAppNotification.user_id == Users.user_id)
    .outerjoin(AreaAlias, and_(
        func.array_length(WhatsAppNotification.areas, 1) > 0,
        AreaAlias.area_id == func.any(WhatsAppNotification.areas),
        AreaAlias.active == True  # Ensures only active areas
    ))
    .outerjoin(SubAreaAlias, and_(
        func.array_length(WhatsAppNotification.sub_areas, 1) > 0,
        SubAreaAlias.sub_area_id == func.any(WhatsAppNotification.sub_areas),
        SubAreaAlias.active == True,  # Ensures only active sub-areas
        SubAreaAlias.area_id == AreaAlias.area_id  # Match sub_areas with the correct area
    ))
    .filter(Users.active == True,WhatsAppNotification.active)  # Only active users
    .group_by(Users.user_id, WhatsAppNotification.phone_numbers, WhatsAppNotification.id)
    .subquery()
)

    users_query = (
    db.session.query(
        Users,
        func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                    "area_id", Area.area_id,
                    "area_name", Area.name
                ) 
            )
        ).filter(Area.area_id != None).label("areas"),
        func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                    "factory_id", Factory.factory_id,
                    "factory_name", Factory.name
                )
            )
        ).filter(Factory.factory_id != None).label("factories"),
        func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                "email_areas", email_notifications_query.c.email_areas,
                "email_sub_areas", email_notifications_query.c.email_sub_areas,
                "alert_emails", email_notifications_query.c.alert_emails,
                "id", email_notifications_query.c.id,
                ) 
            )
        )
        .label("email_notifications"), 
        # Subquery for WhatsApp notifications with aggregation
         func.array_agg(
            func.distinct(
                func.jsonb_build_object(
                "whatsapp_areas", whatsapp_notifications_query.c.whatsapp_areas,
                "whatsapp_sub_areas", whatsapp_notifications_query.c.whatsapp_sub_areas,
                "phone_numbers", whatsapp_notifications_query.c.phone_numbers,
                "id", whatsapp_notifications_query.c.id
                ) 
            )
        )
        .label("whatsapp_notifications")
    )
    .outerjoin(UserArea, UserArea.user_id == Users.user_id)  # Ensure join condition for areas
    .outerjoin(Area, Area.area_id == UserArea.area_id)  # Add join condition for Area
    .outerjoin(UserFactory, UserFactory.user_id == Users.user_id)  # Ensure join condition for factories
    .outerjoin(Factory, Factory.factory_id == UserFactory.factory_id)  # Add join condition for Factory
    .outerjoin(email_notifications_query, email_notifications_query.c.user_id == Users.user_id)
    .outerjoin(whatsapp_notifications_query, whatsapp_notifications_query.c.user_id == Users.user_id)
    .filter(Users.active == True)  # Only active users
    .group_by(Users.user_id)  # Grouping by the necessary columns
    .all() 
   )  
     
    print("Len: ", len(users_query))
    results=[]
    for record in users_query: 
        get_user=record[0] 
        # print("\n Email: ",record[3])
        results.append({    
                       "id": get_user.user_id,
                        "email": get_user.email,
                        "name": get_user.name if get_user.name else "",
                        "mobile_no": get_user.mobile_no if get_user.get_user else "",
                        "role_name": get_user.roles.role.role_name if get_user.roles else "" ,
                        "role_id": get_user.roles.role_id if get_user.roles else "",
                        "status": get_user.active, 
                        "factories": record[2] if record[2] else [], 
                        "areas": record[1] if record[1] else [], 
                        "created_at": get_user.created_at.strftime("%Y-%m-%d %I:%M %p"),
                        "updated_at": get_user.updated_at.strftime("%Y-%m-%d %I:%M %p") if get_user.updated_at else "", 
                        
                         "whatsapp_notifications": [
                            {
                                "toggle": True if notif["id"] else False,
                                "areas": notif["whatsapp_areas"] if notif["whatsapp_areas"] else [],
                                "sub_areas": notif["whatsapp_sub_areas"] if notif["whatsapp_sub_areas"] else [],
                                "id": notif["id"],
                                "phone_numbers": notif["phone_numbers"]["phone_numbers"] if "phone_numbers" in notif and notif["phone_numbers"] and "phone_numbers" in notif["phone_numbers"] else []
                            } for notif in record[4]
                        ],
                        "email_notifications": [
                            {
                                "toggle": True if notif["id"] else False,
                                "areas": notif["email_areas"] if notif["email_areas"] else [],
                                "sub_areas": notif["email_sub_areas"] if notif["email_sub_areas"] else [],
                                "id": notif["id"],
                                "alert_emails": notif["alert_emails"]["alert_emails"] if "alert_emails" in notif and notif["alert_emails"] and "alert_emails" in notif["alert_emails"] else []
                            } for notif in record[3]
                        ]
                        })
        
    return results



def get_user_data(user_id):  
    
    AreaAlias = aliased(Area)
    SubAreaAlias = aliased(SubArea)
    
    email_notifications_query = (
        db.session.query(
            Users.user_id,
            EmailNotification.alert_emails,
            EmailNotification.id,
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "area_id", AreaAlias.area_id,
                        "area_name", AreaAlias.name,
                    )
                )
            )
            .filter(AreaAlias.area_id != None)
            .label("email_areas"),
            
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "sub_area_id", SubAreaAlias.sub_area_id,
                        "sub_area_name", SubAreaAlias.name
                    )
                )
            )
            .filter(SubAreaAlias.sub_area_id != None)
            .label("email_sub_areas")
        )
        .outerjoin(EmailNotification, EmailNotification.user_id == Users.user_id)
        .outerjoin(AreaAlias, and_(
            func.array_length(EmailNotification.areas, 1) > 0,
            AreaAlias.area_id == func.any(EmailNotification.areas),
            AreaAlias.active == True
        ))
        .outerjoin(SubAreaAlias, and_(
            func.array_length(EmailNotification.sub_areas, 1) > 0,
            SubAreaAlias.sub_area_id == func.any(EmailNotification.sub_areas), 
            SubAreaAlias.active == True,
            SubAreaAlias.area_id == AreaAlias.area_id
        ))
        .filter(Users.user_id == user_id, Users.active == True, EmailNotification.active)
        .group_by(Users.user_id, EmailNotification.alert_emails, EmailNotification.id)
        .subquery()
    )
    
    whatsapp_notifications_query = (
        db.session.query(
            Users.user_id,
            WhatsAppNotification.phone_numbers,
            WhatsAppNotification.id,
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "area_id", AreaAlias.area_id,
                        "area_name", AreaAlias.name,
                    )
                )
            )
            .filter(AreaAlias.area_id != None)
            .label("whatsapp_areas"),
            
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "sub_area_id", SubAreaAlias.sub_area_id,
                        "sub_area_name", SubAreaAlias.name
                    )
                )
            )
            .filter(SubAreaAlias.sub_area_id != None)
            .label("whatsapp_sub_areas")
        )
        .outerjoin(WhatsAppNotification, WhatsAppNotification.user_id == Users.user_id)
        .outerjoin(AreaAlias, and_(
            func.array_length(WhatsAppNotification.areas, 1) > 0,
            AreaAlias.area_id == func.any(WhatsAppNotification.areas),
            AreaAlias.active == True
        ))
        .outerjoin(SubAreaAlias, and_(
            func.array_length(WhatsAppNotification.sub_areas, 1) > 0,
            SubAreaAlias.sub_area_id == func.any(WhatsAppNotification.sub_areas),
            SubAreaAlias.active == True,
            SubAreaAlias.area_id == AreaAlias.area_id
        ))
        .filter(Users.user_id == user_id, Users.active == True, WhatsAppNotification.active)
        .group_by(Users.user_id, WhatsAppNotification.phone_numbers, WhatsAppNotification.id)
        .subquery()
    )

    user_data = (
        db.session.query(
            Users,
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "area_id", Area.area_id,
                        "area_name", Area.name
                    ) 
                )
            ).filter(Area.area_id != None).label("areas"),
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "factory_id", Factory.factory_id,
                        "factory_name", Factory.name
                    )
                )
            ).filter(Factory.factory_id != None).label("factories"),
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "email_areas", email_notifications_query.c.email_areas,
                        "email_sub_areas", email_notifications_query.c.email_sub_areas,
                        "alert_emails", email_notifications_query.c.alert_emails,
                        "id", email_notifications_query.c.id,
                    ) 
                )
            ).label("email_notifications"),
            func.array_agg(
                func.distinct(
                    func.jsonb_build_object(
                        "whatsapp_areas", whatsapp_notifications_query.c.whatsapp_areas,
                        "whatsapp_sub_areas", whatsapp_notifications_query.c.whatsapp_sub_areas,
                        "phone_numbers", whatsapp_notifications_query.c.phone_numbers,
                        "id", whatsapp_notifications_query.c.id
                    ) 
                )
            ).label("whatsapp_notifications")
        )
        .outerjoin(UserArea, UserArea.user_id == Users.user_id)
        .outerjoin(Area, Area.area_id == UserArea.area_id)
        .outerjoin(UserFactory, UserFactory.user_id == Users.user_id)
        .outerjoin(Factory, Factory.factory_id == UserFactory.factory_id)
        .outerjoin(email_notifications_query, email_notifications_query.c.user_id == Users.user_id)
        .outerjoin(whatsapp_notifications_query, whatsapp_notifications_query.c.user_id == Users.user_id)
        .filter(Users.user_id == user_id, Users.active == True)
        .group_by(Users.user_id)
        .first()
    )

    get_user = user_data[0] if user_data else {}
    result = {
        "id": get_user.user_id,
        "email": get_user.email,
        "name": get_user.name if get_user.name else "",
        "mobile_no": get_user.mobile_no if get_user.mobile_no else "",
        "role_name": get_user.roles.role.role_name if get_user.roles else "",
        "role_id": get_user.roles.role_id if get_user.roles else "",
        "status": get_user.active,
        "factories": user_data[2] if user_data[2] else [],
        "areas": user_data[1] if user_data[1] else [],
        "created_at": get_user.created_at.strftime("%Y-%m-%d %I:%M %p"),
        "updated_at": get_user.updated_at.strftime("%Y-%m-%d %I:%M %p") if get_user.updated_at else "",
        "whatsapp_notifications": [
            {
                "toggle": True if notif["id"] else False,
                "areas": notif["whatsapp_areas"] if notif["whatsapp_areas"] else [],
                "sub_areas": notif["whatsapp_sub_areas"] if notif["whatsapp_sub_areas"] else [],
                "id": notif["id"],
                "phone_numbers": notif["phone_numbers"]["phone_numbers"] if "phone_numbers" in notif and notif["phone_numbers"] and "phone_numbers" in notif["phone_numbers"] else []
            } for notif in user_data[4]
        ],
        "email_notifications": [
            {
                "toggle": True if notif["id"] else False,
                "areas": notif["email_areas"] if notif["email_areas"] else [],
                "sub_areas": notif["email_sub_areas"] if notif["email_sub_areas"] else [],
                "id": notif["id"],
                "alert_emails": notif["alert_emails"]["alert_emails"] if "alert_emails" in notif and notif["alert_emails"] and "alert_emails" in notif["alert_emails"] else []
            } for notif in user_data[3]
        ],
    }
    
    return result

def add_log(data):
    newData = deepcopy(data)
    if data['area_id']:
        newData['area_target_id'], newData['timestamp'] = Targets.get_latest_target_info_by_area_id(data['area_id'])
    elif data['camera_id']:
        newData['camera_target_id'], newData['timestamp'] = CameraTargets.get_latest_target_info_by_camera_id(data['camera_id'])
    
    try:
        ActivityLog.create(newData)
        return {
            "success": True,
            "message": "Targets updated successfully",
        }
    except Exception as e:
        return {"success": False, "message": str(e)}
    
def convert_seconds_to_minutes_seconds(total_seconds):
    minutes, seconds = divmod(total_seconds, 60)
    return minutes, seconds

def user_log_data():
    userAliased = aliased(Users)
    userRoleAliased = aliased(UserRole)
    roleAliased = aliased(Role)

    query = db.session.query(
        UserLog,
        userAliased.name.label('userName_value'),
        userAliased.active.label('userActive_value'),
        userAliased.email.label('userEmail_value'),
        roleAliased.role_name.label('userRoleName_value')
    ).join(
        userAliased, userAliased.user_id == UserLog.user_id, isouter=True
    ).join(
        userRoleAliased, userRoleAliased.user_id == userAliased.user_id, isouter=True
    ).join(
        roleAliased, roleAliased.role_id == userRoleAliased.role_id, isouter=True
    )

    results = []

    for record in query:
        if record.UserLog.logout_time:
            duration_time_minutes, duration_time_seconds = convert_seconds_to_minutes_seconds(record.UserLog.duration_time)
            accumulated_duration_minutes, accumulated_duration_seconds = convert_seconds_to_minutes_seconds(record.UserLog.accumulated_duration)
            results.append({
                'logID':record.UserLog.log_id,
                'userID':record.UserLog.user_id,
                'userName':record[1],
                'active':record[2],
                'userEmail':record[3],
                'userRole':record[4],
                'loginTime':record.UserLog.login_time.strftime('%H:%M:%S'),
                'logoutTime':record.UserLog.logout_time.strftime('%H:%M:%S'),
                'durationTime':f'{duration_time_minutes} minutes {duration_time_seconds} seconds',
                'accumulatedTime':f'{accumulated_duration_minutes} minutes {accumulated_duration_seconds} seconds'
            })
        else:
            if not record.UserLog.accumulated_duration:
                results.append({
                    'logID':record.UserLog.log_id,
                    'userID':record.UserLog.user_id,
                    'userName':record[1],
                    'active':record[2],
                    'userEmail':record[3],
                    'userRole':record[4],
                    'loginTime':record.UserLog.login_time.strftime('%H:%M:%S'),
                    'logoutTime':'pending',
                    'duration':'pending',
                    'accumulatedTime':'pending'
                })
            else:
                accumulated_duration_minutes, accumulated_duration_seconds = convert_seconds_to_minutes_seconds(record.UserLog.accumulated_duration)
                results.append({
                    'logID':record.UserLog.log_id,
                    'userID':record.UserLog.user_id,
                    'userName':record[1],
                    'active':record[2],
                    'userEmail':record[3],
                    'userRole':record[4],
                    'loginTime':record.UserLog.login_time.strftime('%H:%M:%S'),
                    'logoutTime':'pending',
                    'duration':'pending',
                    'accumulatedTime':f'{accumulated_duration_minutes} minutes {accumulated_duration_seconds} seconds'
                })

    return results

def add_log(data):
    newData = deepcopy(data)
    if data['area_id']:
        newData['area_target_id'], newData['timestamp'] = Targets.get_latest_target_info_by_area_id(data['area_id'])
    elif data['camera_id']:
        newData['camera_target_id'], newData['timestamp'] = CameraTargets.get_latest_target_info_by_camera_id(data['camera_id'])
    
    try:
        ActivityLog.create(newData)
        return {
            "success": True,
            "message": "Targets updated successfully",
        }
    except Exception as e:
        return {"success": False, "message": str(e)}