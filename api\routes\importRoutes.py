from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.hseModels import *
from api.models.annotationModels import *

from api.payloads.importPayloads import *
from api import import_ns
from api.controllers import importController as import_func

from api import db, mqtt_client
from api.services import token_required, user_token_required
from sqlalchemy import and_, or_
import json


@import_ns.route("/cameras")
@import_ns.doc("Import Cameras")
class ImportCameras(Resource):
    #@user_token_required 
    @import_ns.expect(import_cameras_payload)
    @import_ns.response(200, "Cameras imported successfully.")
    #@import_ns.doc(security=["Authorization", "Token-Type"])
    def put(self): 
        data = import_ns.payload
        try:
            req_fields = ["client_id","factory_id","import_url"]
            for field in req_fields:
                if field not in data and not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            get_client= Client.get_by_id(data["client_id"]) 
            if not get_client:
                return {"message": "Client does not exist"}, 400
                
            get_factory= Factory.get_by_id(data["factory_id"]) 
            if not get_factory:
                return {"message": "Factory does not exist"}, 400
            
            results = import_func.import_cameras(data, client=get_client, factory=get_factory)
            
            return {"message": "Cameras imported successfully.", "data": results}, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
        