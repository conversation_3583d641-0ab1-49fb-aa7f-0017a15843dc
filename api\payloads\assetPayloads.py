from api import asset_ns
from flask_restx import fields
from werkzeug.datastructures import FileStorage

def add_argument_to_parser(parser, name, **kwargs):
    parser.add_argument(name, **kwargs)
    
    
# Client

add_client_payload = asset_ns.parser()
client_add_arguments = [
    {
        "name": "file",
        "location": "files",
        "type": FileStorage,
        "help": "Client Logo to upload",
        "required": False,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Client Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Client Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]

for arg in client_add_arguments:
    add_argument_to_parser(add_client_payload, **arg)
    
    
client_update_arguments = [
    {
        "name": "file",
        "location": "files",
        "type": FileStorage,
        "help": "Client Logo to upload",
        "required": False,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Client Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Client Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]
    
    
# Factory





add_factory_payload = asset_ns.parser()
arguments = [
    {
        "name": "client_id",
        "type": int,
        "required": True,
        "help": "Client ID",
        "default": 1,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Factory Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Factory Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]

for arg in arguments:
    add_argument_to_parser(add_factory_payload, **arg)


factory_update_arguments = [
    {
        "name": "file",
        "location": "files",
        "type": FileStorage,
        "help": "Factory Logo to upload",
        "required": False,
    },
    {
        "name": "user_id",
        "type": int,
        "required": True,
        "help": "User ID",
        "default": 23,
    },
    {
        "name": "factory_id",
        "type": int,
        "required": True,
        "help": "Factory ID",
        "default": 10,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Factory Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Factory Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]

update_factory_payload = asset_ns.parser()
for arg in factory_update_arguments:
    add_argument_to_parser(update_factory_payload, **arg)


# User Factory

add_user_factory_payload = asset_ns.model(
    "Add User Factory ",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated factory ID"
        ),
    },
)


update_user_factory_payload = asset_ns.model(
    "Update User Factory",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated factory ID"
        ),
    },
)

delete_user_factory_payload = asset_ns.model(
    "Delete User Factory ",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated factory ID"
        ),
    },
)


# Area

add_area_payload = asset_ns.parser()
area_add_arguments = [
    {
        "name": "factory_id",
        "type": int,
        "required": True,
        "help": "Factory ID",
        "default": 7,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Area Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Area Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]

for arg in area_add_arguments:
    add_argument_to_parser(add_area_payload, **arg)

area_update_arguments = [
    {
        "name": "file",
        "location": "files",
        "type": FileStorage,
        "help": "Area Logo to upload",
        "required": False,
    },
    {
        "name": "factory_id",
        "type": int,
        "required": True,
        "help": "Factory ID",
        "default": 7,
    },
    {
        "name": "area_id",
        "type": int,
        "required": True,
        "help": "Area ID",
        "default": 10,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Area Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Area Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]


update_area_payload = asset_ns.parser()
for arg in area_update_arguments:
    add_argument_to_parser(update_area_payload, **arg)


# User Area

add_user_area_payload = asset_ns.model(
    "Add User Area ",
    {
        "area_id": fields.Integer(
            default=1, required=True, description="The associated area ID"
        ),
    },
)
delete_user_area_payload = asset_ns.model(
    "Delete User Area ",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "area_id": fields.Integer(
            default=1, required=True, description="The associated area ID"
        ),
    },
)



# Sub Area

add_multiple_sub_areas_payload = asset_ns.model(
    "Add Multiple Sub Areas Payload",
    {

        "areas": fields.List(
            fields.Nested(
                asset_ns.model(
                    "Area Sub Areas",
                    {
                        "area_id": fields.Integer(
                            required=True, description="The Area ID"
                        ),
                        "sub_areas": fields.List(
                            fields.String,
                            required=True,
                            description="List of Sub Areas",
                        ),
                    },
                )
            ),
            required=True,
            description="A list of areas and their sub-areas",
        ),
    },
)


add_sub_area_payload = asset_ns.parser()
sub_area_add_arguments = [

    {
        "name": "area_id",
        "type": int,
        "required": True,
        "help": "Area ID",
        "default": 1,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Sub Area Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Area Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]

for arg in sub_area_add_arguments:
    add_argument_to_parser(add_sub_area_payload, **arg)

sub_area_update_arguments = [

    {
        "name": "sub_area_id",
        "type": int,
        "required": True,
        "help": "Sub Area ID",
        "default": 10,
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Area Name",
        "default": "TBA",
    },
    {
        "name": "address",
        "type": str,
        "required": True,
        "help": "Area Address",
        "default": "Markaz, F-7/4, Islamabad",
    },
]


update_sub_area_payload = asset_ns.parser()
for arg in sub_area_update_arguments:
    add_argument_to_parser(update_sub_area_payload, **arg)

delete_multiple_sub_area_payload = asset_ns.model(
    "Delete Multiple Sub Area",
    {

        "sub_area_ids": fields.List(
            fields.Integer,
            required=True,
            description="The List of Sub Area IDs",
        ),
    },
)



# User Cameras Payload
get_user_cameras = asset_ns.model(
    "Get User Cameras",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "factory_id": fields.Integer(
            default=0, required=True, description="The associated User ID"
        ),
        "status": fields.String(
            default="active", description="The cameras status"
        ),
        "connectivity":fields.String(
            default="1", description="The cameras connectivity status (1 - Connected , 2- Not Connected)"
        ),
        "areas": fields.List(
            fields.Integer,
            default=[],
           description="The List of Area IDs"
        ),
        "sub_areas": fields.List(
            fields.Integer,
            default=[],
           description="The List of Sub Area IDs"
        ),
        "module": fields.Integer(
             description="The AI Model"
        ),
        "pagination": fields.Nested(asset_ns.model('Cameras Pagination', {
            "page_no": fields.Integer(default=1, description="Page No."),
            "per_page": fields.Integer(default=20, description="Records per page"),
        }), required=True ,description="Pagination for Cameras"),  
    },
)



get_live_cameras_payload = asset_ns.model(
    "Get Live Cameras",
    {
        "user_id": fields.Integer(
            default=1,description="The associated User ID"
        ),   
        "date": fields.Date(
            default="2024-09-25", description="The selected date"
        ),        
    },
)




uam_camera_model = asset_ns.model(
    "UAM Camera Model",
    {
        "camera_id": fields.String(
            default="NVR-124", required=True, description="The Unique Camera ID"
        ),
        "camera_ip": fields.String(
            default="************",
            required=True,
            description="The Camera IP",
        ),
        "modules": fields.List(
            fields.Integer,
            required=True,
            description="The associated modules",
        ),
        "sub_area_id": fields.Integer(
            default=1, required=True, description="The associated Sub Area ID"
        ),
        "camera_name": fields.String(
            default="Camera 1", required=True, description="The Camera Name"
        ),
        "camera_position_no": fields.String(
            default="Position 1", required=True, description="The Camera Position Number"
        ),
        "nvr_no": fields.Integer(
            default=1, required=True, description="The NVR Number"
        ),
        "camera_location": fields.String(
            default="Location 1", required=True, description="The Camera Location"
        ),
        "username": fields.String(
            default="admin", required=True, description="The Camera Username"
        ),
        "password": fields.String(
            default="password", required=True, description="The Camera Password"
        ),
        "stream": fields.String(
            default="rtsp://camera_stream", required=True, description="The Camera Stream"
        ),
        "port": fields.Integer(
            default=8080, required=True, description="The Camera Port Number"
        ),
    },
)

# Add Cameras by Excel Payload
add_camera_with_excel_payload = asset_ns.model(
    "Add Cameras with Excel Sheet",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "cameras_list": fields.List(
            fields.Nested(uam_camera_model), required=True, description="List of Alerts"
        ),
        "factory_name": fields.String(
            default="", required=True, description="The Factory Name"
        ),
        "area_name": fields.String(
            default="", required=True, description="The Area Name"
        ),
        "area_owner": fields.String(
            default="", required=True, description="The Area Owner Name"
        ),
    },
)



# Update Camera Payload
update_camera_payload = asset_ns.model(
    "Update Camera",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "old_camera_id": fields.String(
            default="NVR-124", required=True, description="The Unique Camera ID"
        ),
        "new_camera_id": fields.String(
            default="NVR-124", required=True, description="The Unique Camera ID"
        ),
        "change_camera_id": fields.Boolean(
            default=False, required=True, description="The identifier for new Camera ID"
        ),
        "camera_ip": fields.String(
            default="************",
            required=True,
            description="The Camera IP",
        ),
        "modules": fields.List(
            fields.Integer,
            required=True,
            description="The associated modules",
        ),
        "sub_area_id": fields.Integer(
            default=1, required=True, description="The associated Sub Area ID"
        ),
        "camera_name": fields.String(
            default="Camera 1", required=True, description="The Camera Name"
        ),
        "camera_position_no": fields.String(
            default="Position 1", required=True, description="The Camera Position Number"
        ),
        "nvr_no": fields.Integer(
            default=1, required=True, description="The NVR Number"
        ),
        "camera_location": fields.String(
            default="Location 1", required=True, description="The Camera Location"
        ),
        "username": fields.String(
            default="admin", required=True, description="The Camera Username"
        ),
        "password": fields.String(
            default="password", required=True, description="The Camera Password"
        ),
        "stream": fields.String(
            default="rtsp://camera_stream", required=True, description="The Camera Stream"
        ),
        "port": fields.Integer(
            default=8080, required=True, description="The Camera Port Number"
        ),
         "status": fields.Integer(
            default=1, required=True, description="The Camera Status (1 - Connected , 2- Not Connected)"
        ),
    },
)


# User Cameras Payload
get_user_cameras = asset_ns.model(
    "Get User Cameras",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "factory_id": fields.Integer(
            default=0, required=True, description="The associated User ID"
        ),
        "status": fields.String(
            default="active", description="The cameras status"
        ),
        "connectivity":fields.String(
            default="1", description="The cameras connectivity status (1 - Connected , 2- Not Connected)"
        ),
        "areas": fields.List(
            fields.Integer,
            default=[],
           description="The List of Area IDs"
        ),
        "sub_areas": fields.List(
            fields.Integer,
            default=[],
           description="The List of Sub Area IDs"
        ),
        "module": fields.Integer(
             description="The AI Model"
        ),
        "pagination": fields.Nested(asset_ns.model('Cameras Pagination', {
            "page_no": fields.Integer(default=1, description="Page No."),
            "per_page": fields.Integer(default=20, description="Records per page"),
        }), required=True ,description="Pagination for Cameras"),  
    },
)



# Add Camera Payload
add_camera_payload = asset_ns.model(
    "Add New Camera",
    {

        "camera_id": fields.String(
            default="NVR-124", required=True, description="The Unique Camera ID"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
        "area_id": fields.Integer(
            default=1, required=True, description="The associated Area ID"
        ),
        "sub_area_id": fields.Integer(
            default=1, required=True, description="The associated Sub Area ID"
        ),
        
        "camera_ip": fields.String(
            default="************",
            required=True,
            description="The Camera IP",
        ),
        "camera_name": fields.String(
            default="Camera 1", required=True, description="The Camera Name"
        ),
        "camera_position_no": fields.String(
            default="Position 1", required=True, description="The Camera Position Number"
        ),
        

        "nvr_no": fields.Integer(
            default=1, required=True, description="The NVR Number"
        ),
        "username": fields.String(
            default="admin", required=True, description="The Camera Username"
        ),
        "password": fields.String(
            default="password", required=True, description="The Camera Password"
        ),
        "stream": fields.String(
            default="rtsp://camera_stream", required=True, description="The Camera Stream"
        ),
        "port": fields.Integer(
            default=8080, required=True, description="The Camera Port Number"
        ),
        
        "modules": fields.List(
            fields.Integer,
            required=True,
            description="The associated modules",
        ),
        

    },
)

# Get Camera Payload
get_camera_payload = asset_ns.model(
    "Get Camera",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
        "window_time_minutes": fields.Integer(
            default=10,
            description="Time window in minutes to consider camera as active"
        ),
        "modules": fields.List(
            fields.Integer,
            required=False,
            description="The list of AI Modules"
        ),
        "pagination": fields.Nested(asset_ns.model('Cameras Pagination', {
            "page_no": fields.Integer(default=1, description="Page No."),
            "per_page": fields.Integer(default=20, description="Records per page"),
        }), required=True ,description="Pagination for Cameras"),  
    },
)

create_update_camera_livefeed_payload = asset_ns.model(
    "Create Update Camera Livefeed",
    {
        "data": fields.List(
            fields.Nested(
                asset_ns.model(
                    "Camera Livefeed",
                    {
                        "camera_id": fields.String(
                            required=True,
                            description="ID of the camera",
                            default="ICF-AO7-BDE-1-6-10-116",
                        ),
                        "client_id": fields.Integer(
                            required=True, description="Client ID", default=1
                        ),
                        "factory_id": fields.Integer(
                            required=True, description="Factory ID", default=1
                        ),
                        "image_url": fields.String(
                            required=True, description="URL of the image"
                        ),
                        "timestamp": fields.String(
                            required=True,
                            description="Timestamp of the image",
                            default="2024-10-15 20:41:02",
                        ),
                    },
                )
            )
        )
    },
)

get_camera_livefeed_payload = asset_ns.model(
    "Get Camera Livefeed",
    {
        "camera_id": fields.String(
            required=True,
            description="ID of the camera",
            default="ICF-AO7-BDE-1-6-10-116",
        ),
        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
    },
)

get_cameras_status_payload = asset_ns.model(
    "Get Cameras Status",
    {
        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
        "window_time_minutes": fields.Integer(
            default=10,
            description="Time window in minutes to consider camera as active"
        )
    },
)


get_videos_summary_payload = asset_ns.model(
    "Get Videos Summary",
    {
        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
    },
)


update_video_status_payload = asset_ns.model(
    "Update Video Status",
    {
        "id": fields.Integer(
            required=True,
            description="ID of the video",
            default=1,
        ),
        "status": fields.String(
            required=True, description="Status of the video"
        ),
    },
)

upload_video_payload = asset_ns.parser()
upload_video_arguments = [
    {
        "name": "client_id",
        "type": int,
        "required": True,
        "help": "Client ID",
        "default": 1,
    },
    {
        "name": "factory_id", 
        "type": int,
        "required": True,
        "help": "Factory ID",
        "default": 1,
    },
    {
        "name": "camera_id",
        "type": str,
        "required": True,
        "help": "Camera ID",
        "default": "ICF-AO7-BDE-1-6-10-116",
    },
    {
        "name": "name",
        "type": str,
        "required": True,
        "help": "Video name",
        "default": "Sample Video",
    },
    {
        "name": "description",
        "type": str,
        "required": False,
        "help": "Video description",
        "default": "Sample description",
    },
    {
        "name": "duration",
        "type": int,
        "required": True,
        "help": "Video duration in seconds",
        "default": 120,
    },
    {
        "name": "size",
        "type": int,
        "required": True,
        "help": "Video size in bytes",
        "default": 1024000,
    },
    {
        "name": "modules",
        "type": str,  # Change from list to str
        "required": True,
        "help": "Comma-separated module IDs (e.g., '1,2,3')",
        "default": "1,2,3",  
},
    {
        "name": "status",
        "type": str,
        "required": True,
        "help": "Video status",
        "default": "uploaded",
    },
    {
        "name": "file",
        "location": "files",
        "type": FileStorage,
        "required": True,
        "help": "Video file to upload",
    },
]

for arg in upload_video_arguments:
    add_argument_to_parser(upload_video_payload, **arg)


get_factory_shifts_payload = asset_ns.model(
    "Get Factory Shifts",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
    },
)

add_shift_payload = asset_ns.model(
    "Add Shift",
    {
        "shift_id": fields.Integer(
            default=0, required=False, description="The Shift ID"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
        "name": fields.String(
            default="Shift A", required=True, description="The Shift Name"
        ),
        "start_time": fields.String(
            default="08:00:00", required=True, description="The Shift Start Time"
        ),
        "end_time": fields.String(
            default="17:00:00", required=True, description="The Shift End Time"
        ),
        "active": fields.Boolean(
            default=True, required=False, description="The Shift Active Status"
        ),
    },
)
