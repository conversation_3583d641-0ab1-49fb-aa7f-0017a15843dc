from flask import jsonify, request
from google.oauth2 import id_token
from google.auth.transport import requests
import jwt
from api import app
from api.models.uamModels import Users
from functools import wraps


def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get("Authorization")
        token_type = request.headers.get("Token-Type")
        if not token or not token_type:
            return {"message": "Token or token type is missing!"}, 401
        if token_type == "jwt":
            try:
                # Verify the token as a JWT
                jwt_data = jwt.decode(token, app.config["SECRET_KEY"], algorithms=["HS256"])
                current_user = Users.get_by_user_id(jwt_data["user_id"])
                if current_user:
                    role = current_user.roles.role.role_name 
                    if role and role.lower() in ['admin', 'it']:
                        kwargs["jwt_data"] = jwt_data
                        return f(*args, **kwargs)
                    else:
                        return {"message": "Access Denied for this user role"}, 403
                else:
                    return {"message": "Token is invalid"}, 401

            except jwt.ExpiredSignatureError:
                return {"message": "Token has expired"}, 401
            except jwt.InvalidTokenError:
                return {"message": "Token is invalid"}, 401
        else:
            return {"message": "Invalid token type"}, 400

    return decorated



def user_token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get("Authorization")
        token_type = request.headers.get("Token-Type")
        if not token or not token_type:
            return {"message": "Token or token type is missing!"}, 401
        if token_type == "jwt":
            try:
                # Verify the token as a JWT
                data = jwt.decode(token, app.config["SECRET_KEY"], algorithms=["HS256"])
                current_user = Users.get_by_user_id(data["user_id"])
                if current_user:
                        return f(*args, **kwargs)
                else:
                    return {"message": "Token is invalid"}, 401
            except jwt.ExpiredSignatureError:
                return {"message": "Token has expired"}, 401
            except jwt.InvalidTokenError:
                return {"message": "Token is invalid"}, 401
        else:
            return {"message": "Invalid token type"}, 400

    return decorated
