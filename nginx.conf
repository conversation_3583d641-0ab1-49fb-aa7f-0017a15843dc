server {
    server_name stage-besafetylens.disruptlabs.tech;
    client_max_body_size 100M;
    location / {
        include proxy_params;
        proxy_pass http://**********:8008;

    }
    # WebSocket proxy
    location /ws/ {
        proxy_pass http://**********:8008; 
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/disruptlabs.tech/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/disruptlabs.tech/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}