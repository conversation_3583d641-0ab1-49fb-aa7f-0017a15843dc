from flask import request
from flask_restx import Resource, abort

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.hseModels import *


from api import hse_ns
from api.payloads.hsePayloads import *
from api.services import token_required

from api.controllers.hseController import *

from api.utils.utils import parse_filters

import traceback
import re



@hse_ns.route("/get_all_modules")
@hse_ns.doc("Get all Modules")
class GetAllModules(Resource):
    # @token_required
    @hse_ns.doc("Get all Modules")
    @hse_ns.response(200, "Modules fetched successfully.")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def get(self):
        get_modules = Modules.get_all_records()
        results = [
        {
            "module_id": module.module_id,
            "module_name": module.name,
        } for module in get_modules
        ]
        return {"message": "Modules fetched successfully.", "data": results}, 200


@hse_ns.route("/add_update_module")
@hse_ns.doc("Add or Update Module")
class AddOrUpdateModule(Resource):
    # @token_required
    @hse_ns.doc("Add or Update Module")
    @hse_ns.expect(add_module_payload)
    @hse_ns.response(200, "Module added or updated successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = hse_ns.payload
        try:
            req_fields = ["name"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
            module, action = Modules.createUpdate(data)
            if action == "updated":
                msg = f"{module.name} module updated successfully"
            else:
                msg = f"{module.name} module added successfully"
            return {
                "message": msg,
                "success": True,
            }, 200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@hse_ns.route("/sync_alerts")
@hse_ns.doc("Sync Alerts")
class SyncAlerts(Resource):
    # @token_required
    @hse_ns.doc("Sync Alerts")
    @hse_ns.expect(insert_alerts_payload)
    @hse_ns.response(200, "Alerts synced successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = hse_ns.payload
        try:
            # Only check for 'alerts' at the root
            if "alerts" not in data or not data["alerts"]:
                return {"message": "alerts is missing."}, 400

            # Required fields inside each alert
            alert_fields = [
                "alert_id", "factory_id", "client_id", "module_id",
                "compliant", "unannotated_url", "annotated_url", "camera_id"
            ]
            for idx, alert in enumerate(data["alerts"]):
                for field in alert_fields:
                    if field not in alert or alert[field] in [None, ""]:
                        return {"message": f"{field} is missing in alert at index {idx}."}, 400

            resp = Alerts.sync_alerts(data["alerts"])

            return {
                "message": "Alerts synced successfully",
                "success": True,
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@hse_ns.route("/get_db_id/<string:client_id>/<string:factory_id>/<string:table_name>")
@hse_ns.doc("Get DB ID")
class HseGetDBID(Resource):
    # @token_required
    @hse_ns.doc("Get DB ID")
    @hse_ns.response(200, "DB ID fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    # @sub_area_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,client_id, factory_id, table_name):
        try:
            if client_id and factory_id and table_name: 
                models=[Alerts] 
                get_table= [x for x in models if x.__tablename__.lower() == table_name.lower()]
                #print("got table: ", get_table)
                if get_table and get_table[0]:
                    
                    resp = get_table[0].get_local_db_id(client_id=client_id, factory_id=factory_id) 
                    table= get_table[0].__tablename__
                    #print("Resp: ", resp)
                    
                    return {
                        "message": "DB ID fetched successfully" if resp is not None else "ID does not exist",
                        "success":  True  if resp is not None else False,
                        "id": resp  if resp is not None else 0, 
                        "table": table
                        }, 200
                else:
                    return {
                    "message": "Table not found" ,
                    "success":  False,
                    }, 400
            else:
                return {
                "message": "Insufficient data" ,
                "success":  False,
                }, 400
                
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@hse_ns.route("/get_hse_alerts_module_wise")
@hse_ns.doc("Filter Data for HSE alerts module wise")
class FilterData(Resource):
    # @token_required
    @hse_ns.doc("Filter Data for HSE alerts zone wise")
    @hse_ns.expect(hse_alerts_zone_wise_payload)
    @hse_ns.response(200, "Data fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    def post(self):
        data = hse_ns.payload
        try:
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required."}, 400
 
            start_date, end_date = parse_filters(data)

            # Logging for debug
            print("Parsed start_date:", start_date)
            print("Parsed end_date:", end_date)
 
            res = get_alert_count_by_module(client_id=client_id,factory_id=factory_id,end_date=end_date,start_date=start_date)

            return {
                "message": "Data fetched successfully.",
                "success": True,
                "data": res
            }, 200

        except Exception as e:
            print("Error in filter_data:", e)
            abort(400, {"success": False, "message": f"Error {e}"})


@hse_ns.route("/get_hse_alerts_by_zone")
@hse_ns.doc("HSE alerts zone wise")
class ZoneStatus(Resource):
    @hse_ns.expect(zone_status_payload)
    @hse_ns.response(200, "HSE alerts zone wise fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    def post(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required."}, 400

            start_date, end_date = parse_filters(data)

            zone_data = get_alert_count_by_zone(client_id, factory_id, start_date, end_date)

            return {
                "message": "HSE alerts zone wise fetched successfully",
                "success": True,
                "data": zone_data
            }, 200

        except Exception as e:
            print("Error in /get_zone_status:", e)
            return {"message": f"Error: {e}", "success": False}, 400
        

@hse_ns.route("/get_ai_model_performance_trend")
@hse_ns.doc("AI Model Performance Trend")
class AIModelPerformanceTrend(Resource):
    @hse_ns.expect(alert_trend_payload)
    @hse_ns.response(200, "Alert trend fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    def post(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required."}, 400 
            
            start_date, end_date = parse_filters(data)

            trend_result = get_alert_trend_by_module(client_id, factory_id, start_date, end_date)

            return {
                "message": "AI Model alert trend fetched successfully",
                "success": True,
                "data": trend_result
            }, 200

        except Exception as e:
            print("Error in /get_ai_model_performance_trend:", e)
            return {"message": f"Error: {e}", "success": False}, 400


@hse_ns.route("/get_hse_summary")
@hse_ns.doc("Get HSE Summary Info")
class GetHseSummary(Resource):
    @hse_ns.expect(hse_summary_payload)
    @hse_ns.response(200, "HSE summary fetched successfully.")
    def post(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400 

            active_zone_data = get_active_zone_summary(client_id, factory_id)
            ai_models={
                "active": 5,         
                "total": 5          
            }

            return {
                "message": "HSE summary fetched successfully",
                "success": True,
                "data": {
                    "active_zones": active_zone_data,
                    "ai_models_active": ai_models,       
                    "monitored_zones": 12        
                }
            }, 200

        except Exception as e:
            print("Error in /get_hse_summary:", e)
            return {"message": f"Error: {e}", "success": False}, 400
        


@hse_ns.route("/get_live_alerts")
@hse_ns.doc("Get Live Alerts (Filtered & Enriched)")
class GetLiveAlerts(Resource):
    @hse_ns.expect(live_alerts_payload)
    @hse_ns.response(200, "Live alerts fetched successfully.")
    def post(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            alerts_data = get_filtered_live_alerts(data)

            return {
                "message": "Live alerts fetched successfully",
                "success": True,
                "data": alerts_data
            }, 200

        except Exception as e:
            print("Error in /get_live_alerts:", e)
            return {"message": f"Error: {e}", "success": False}, 400

@hse_ns.route("/add_factory_module")
@hse_ns.doc("Add or Update Module")
class AddOrUpdateModule(Resource):
    # @token_required
    @hse_ns.doc("Add or Update Module")
    @hse_ns.expect(add_factory_module_payloads)
    @hse_ns.response(200, "Module added or updated successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def post(self):
        data = hse_ns.payload
        try:
            req_fields = ["module_ids"]
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400

            factory_id = data.get("factory_id")
            module_ids = data.get("module_ids", [])

            results = []
            for module_id in module_ids:
                module, created = Factory_Modules.createUpdate(factory_id, module_id)
                if created:
                    results.append(f"Module {module.module_id} added successfully")
                else:
                    results.append(f"Module {module.module_id} updated successfully")

            return {
                "message": results,
                "success": True
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})

@hse_ns.route("/get_factory_modules/<int:factory_id>")
@hse_ns.doc("Get Factory Modules")
class GetFactoryModules(Resource):
    # @token_required
    @hse_ns.doc("Get Factory Modules")
    @hse_ns.response(200, "Factory Modules retrieved successfully.")
    @hse_ns.response(400, "Validation Error")
    # @hse_ns.doc(security=["Authorization", "Token-Type"])
    def get(self, factory_id):
        try:
            modules = Factory_Modules.get_modules_by_factory_id(factory_id)
            return {
                "message": "Factory Modules retrieved successfully.",
                "success": True,
                "data": modules
            }, 200

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



@hse_ns.route("/get_stats")
@hse_ns.doc("Get Live Alerts (Filtered & Enriched)")
class GetStats(Resource):
    @hse_ns.expect(live_alerts_payload)
    @hse_ns.response(200, "Live alerts fetched successfully.")
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            statsData = get_stats(data)

            return {
                "message": "Live alerts fetched successfully",
                "success": True,
                "data": statsData
            }, 200

        except Exception as e:
            print("Error in /get stats:", e)
            return {"message": f"Error: {e}", "success": False}, 400



@hse_ns.route("/get_alert_trend")
@hse_ns.doc("get Alert trend")
class GetAlertTrend(Resource):
    @hse_ns.expect(live_alerts_payload)
    @hse_ns.response(200, "Live alerts fetched successfully.")
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            statsData = get_alert_trend(data)

            return {
                "message": "Live alerts fetched successfully",
                "success": True,
                "data": statsData
            }, 200

        except Exception as e:
            print("Error in /get stats:", e)
            return {"message": f"Error: {e}", "success": False}, 400


@hse_ns.route("/get_alert_trend_analytics")
@hse_ns.doc("get Alert trend analytics")
class GetAlertTrendAnalytics(Resource):
    @hse_ns.expect(dynamic_alert_trend_analytics_payload)
    @hse_ns.response(200, "Alert trend analytics fetched successfully.")
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id") 

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            result = get_alert_trend_analytics(data)

            return {
                "message": "Live alerts fetched successfully",
                "success": True,
                "data": result
            }, 200

        except Exception as e:
            print("Error fetching alert trend analytics:", e)
            return {"message": f"Error: {e}", "success": False}, 400


@hse_ns.route("/get_overall_compliance_score")
@hse_ns.doc("Get Overall Compliance Score")
class ZoneComplianceScore(Resource):
    @hse_ns.expect(overall_compliance_score_payload)
    @hse_ns.response(200, "Overall compliance score fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required."}, 400

            compliance_data = get_overall_compliance_score(data)

            return {
                "message": "Zone compliance score fetched successfully",
                "success": True,
                "data": compliance_data
            }, 200

        except Exception as e:
            print("Error in fetching overall compliance score:", e)
            return {"message": f"Error: {e}", "success": False}, 400
        


@hse_ns.route("/live_analytics_heatmap")
@hse_ns.doc("Dynamic Heatmap for HSE Alerts")
class DynamicHeatmap(Resource):
    @hse_ns.expect(dynamic_heatmap_payload)
    @hse_ns.response(200, "Dynamic heatmap data fetched successfully.")
    @hse_ns.response(400, "Validation Error")
    def post(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")
            x_axis = data.get("x_axis")
            y_axis = data.get("y_axis")

            if not all([client_id, factory_id, x_axis, y_axis]):
                return {
                    "message": "client_id, factory_id, x_axis, and y_axis are required.",
                    "success": False
                }, 400

            heatmap_data = get_dynamic_heatmap_data(data)
            
            if "error" in heatmap_data:
                return {
                    "message": heatmap_data["error"],
                    "success": False
                }, 400

            return {
                "message": "Dynamic heatmap data fetched successfully",
                "success": True,
                "data": heatmap_data
            }, 200

        except Exception as e:
            print("Error in fetching dynamic heatmap data:", e)
            return {
                "message": f"Error: {str(e)}",
                "success": False
            }, 500


@hse_ns.route("/get_months_accepted_records")
@hse_ns.doc("Get Months Accepted Records")
class GetMonthsAcceptedRecords(Resource):
    @hse_ns.expect(get_months_accepted_records_payload)
    @hse_ns.response(200, "Months accepted records fetched successfully.")
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            accepted_records = get_accepted_records_monthly(data)

            return {
                "message": "Months accepted records fetched successfully",
                "success": True,
                "data": accepted_records
            }, 200

        except Exception as e:
            print("Error in fetching months accepted records:", e)
            return {"message": f"Error: {e}", "success": False}, 400
        

@hse_ns.route("/compliance_summary")
class ComplianceSummary(Resource):
    @hse_ns.doc("Get compliance summary")
    @hse_ns.expect(compliance_summary_payload)
    def put(self):
        """
        Returns the compliance data for total alerts chart.
        """
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  

            result = get_compliance_data_for_modules(data)
            return result

        except Exception as e:
            traceback.print_exc()
            print(f"Exception in Compliance Summary: {e}")
            return {"totalAlertsChart": []}
        


@hse_ns.route("/compliance_accuracy_fetch")
class ComplianceAccuracyFetch(Resource):
    @hse_ns.doc("Compliance Accuraccy Fetch")
    @hse_ns.expect(compliance_accuracy_fetch_payload)
    def put(self):
        """
        Returns the compliance data for total alerts chart.
        """
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")

            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  

            result = get_compliance_accuracy_data(data)

            return result
        except Exception as e:
            traceback.print_exc()
            print(f"Exception in Compliance Summary: {e}")
            return {}
        

@hse_ns.route("/get_factory/<int:id>")
@hse_ns.doc("Get Factory by ID")
class GetFactory(Resource):
    # @user_token_required
    @hse_ns.doc("Get factory by ID")
    @hse_ns.response(200, "Factory fetched successfully.")
    @hse_ns.response(400, "Validation Error") 
    @hse_ns.doc(security=["Authorization", "Token-Type"]) 
    def get(self, id):
        try:
            if not id:
                    return {"message": f"Factory ID is required."}, 400
                
            # resp = Factory.get_by_id(id)
            resp = "RYK"
            if resp is not None:
                    
                return {
                    "message":  "Factory fetched successfully",
                    "success":  True,
                    # "data":{
                    #         "id": resp.factory_id,
                    #         "name": resp.name,
                    #         "created_at": resp.created_at.strftime("%Y-%m-%d"),
                    #         "updated_at": resp.updated_at.strftime("%Y-%m-%d"),
                    #         "active": resp.active,
                    #         "areas": [ { 
                    #                 "id": x.area_id,  
                    #                 "area": x.name,
                    #                 "active": x.active if x.active else False, 
                    #                 "sub_area": [ {"id": sub.sub_area_id, "name": sub.name,} for sub in x.sub_areas if sub.active],
                    #                 "areaOwner": (next((y.user.name for y in x.users_areas if y and y.user and y.user.roles.role_id == 8),""))
                    #         } for x in resp.factory_areas]
                    #     }

                    "data": {
                            "id": 32,
                            "name": "RYK",
                            "created_at": "2024-12-11",
                            "updated_at": "2024-12-11",
                            "active": True,
                            "areas": [
                                {
                                    "id": 21,
                                    "area": "Personal-Products",
                                    "active": True,
                                    "sub_area": [
                                        {
                                            "id": 156,
                                            "name": "PP Shubham S-01"
                                        },
                                        {
                                            "id": 157,
                                            "name": "PM Area"
                                        },
                                        {
                                            "id": 158,
                                            "name": "PP Proces B-Area"
                                        },
                                        {
                                            "id": 159,
                                            "name": "RBL Conveyor"
                                        },
                                    ],
                                    "areaOwner": "Rafay Waqar"
                                },
                            ]
                        }
                    }, 200
            else:
                return {
                "message": "Factory does not exist" ,
                "success":  False,
                
                }, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@hse_ns.route("/compliance_by_camera_summary")
class ComplianceByCameraSummary(Resource):
    @hse_ns.doc("Get compliance by camera summary")
    @hse_ns.expect(compliance_by_camera_summary_payload)
    def put(self):
        try:
            data = hse_ns.payload
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")
            
            if not client_id or not factory_id:
                return {"message": "client_id and factory_id are required"}, 400  
            
            result = get_cameras_compliance(data)

            return result
        
        except Exception as e:
            traceback.print_exc()
            print(f"Exception in Compliance Summary: {e}")
            return {}