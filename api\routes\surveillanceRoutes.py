from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.surveillanceModels import *

from api.payloads.surveillancePayloads import *

from api import surveillance_ns
from api.controllers import surveillanceController

from api import db
from api.services import token_required, user_token_required
from sqlalchemy import and_, or_

import re
@surveillance_ns.route("/sync_rows")
@surveillance_ns.doc("Sync Rows")
class SyncRows(Resource):
    @surveillance_ns.doc("Sync Rows")
    @surveillance_ns.expect(surveillance_sync_payload_model)
    @surveillance_ns.response(200, "Rows Synced successfully.")
    @surveillance_ns.response(400, "Validation Error")
    def post(self):
        data = surveillance_ns.payload
        try:
            models=[SurveillanceStaffEvents,SurveillanceCustomerCounts] 
            for field in models:
                if field.__tablename__ in data.keys():
                    rows = data[field.__tablename__]
                    any_rows_added = field.add_multiple_rows(rows) 
                    
             # Generate the appropriate message
            if any_rows_added:
                message = "Rows Synced successfully."
            else:
                message = "No rows were added."
            
            response = {"message": message}  
                
            return response, 200 

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


@surveillance_ns.route("/insert_interval_images")
@surveillance_ns.doc("Insert Interval Images")
class InsertIntervalImages(Resource):
    @surveillance_ns.doc("Insert Interval Images")
    @surveillance_ns.expect(surveillance_insert_interval_images_payload_model)
    @surveillance_ns.response(200, "Images inserted successfully.")
    @surveillance_ns.response(400, "Validation Error")
    def post(self):
        data = surveillance_ns.payload
        try:
            # The data should contain a list of dictionaries, each with camera_id, image_url and timestamp
            # Example: [{"camera_id": 1, "image_url": "http://example.com/image1.jpg", "timestamp":"2024-10-19 22:45:23"}, {...}]
           
            if "images" in data and data["images"]:
                not_found_cameras=SurveillanceLiveCameraImages.add_images_in_interval(data["images"])

                return {"message": "Few Cameras Not Found" if  len(not_found_cameras) > 0 else "Images inserted successfully",
                        "data": not_found_cameras if  len(not_found_cameras) > 0 else None
                        }, 400  if len(not_found_cameras) > 0 else 200
            else:
                return {"message": "Invalid payload, 'images' field missing"}, 400

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error inserting images: {e}"})
            
            
@surveillance_ns.route("/get_db_id/<string:client_id>/<string:factory_id>/<string:table_name>")
@surveillance_ns.doc("Get DB ID")
class GetDBID(Resource):
    # @token_required
    @surveillance_ns.doc("Get DB ID")
    @surveillance_ns.response(200, "DB ID fetched successfully.")
    @surveillance_ns.response(400, "Validation Error")
    # @sub_area_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,client_id, factory_id, table_name):
        try:
            if client_id and factory_id and table_name: 
                models=[SurveillanceStaffEvents,SurveillanceCustomerCounts] 
                get_table= [x for x in models if x.__tablename__.lower() == table_name.lower()]
                print("got table: ", get_table)
                if get_table and get_table[0]:
                    
                    resp = get_table[0].get_local_db_id(client_id=client_id, factory_id=factory_id) 
                    table= get_table[0].__tablename__
                    print("Resp: ", resp)
                    
                    return {
                        "message": "DB ID fetched successfully" if resp is not None else "ID does not exist",
                        "success":  True  if resp is not None else False,
                        "id": resp  if resp is not None else 0, 
                        "table": table
                        }, 200
                else:
                    return {
                    "message": "Table not found" ,
                    "success":  False,
                    }, 400
            
            else:
                return {
                "message": "Insufficient data" ,
                "success":  False,
                }, 400
            
                

        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})


# ---------------------------- Monitoring Feed API ----------------------------
@surveillance_ns.route("/monitoring_feed")
@surveillance_ns.doc("Get Monitoring Field Data ")
class GetMonitoringFeed(Resource):
    # @user_token_required
    @surveillance_ns.expect(monitoring_feed_payload)
    @surveillance_ns.response(200, "Monitoring Field data fetched successfully.")
    @surveillance_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
       Shows Monitoring Field Data:
        Input:
        - factory_id
        - client_id 
        Output:
        - image
        - Staff Status (Absent / Present)
        - Customer Count
        - time
        """
        data = surveillance_ns.payload
        try:
            req_fields = ["factory_id", "client_id"] # leaving array here incase multiple fields are required in the future 
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
        
            resp = surveillanceController.monitoring_feed(data)
            return {"message": "Monitoring Field data fetched successfully.",
                    "success": True,
                    "data": resp    
            },200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



# ---------------------------- Analytics Report API ----------------------------
@surveillance_ns.route("/analytics_report")
@surveillance_ns.doc("Analytics Report Data ")
class AnalyticsReport(Resource):
    # @user_token_required
    @surveillance_ns.expect(filters_payload)
    @surveillance_ns.response(200, "Analytics Report data fetched successfully.")
    @surveillance_ns.response(400, "Validation Error")
    # @factory_dashboard_ns.doc(security=["Authorization", "Token-Type"])
    def put(self):
        """
       Shows Monitoring Field Data:
        Input:
        - factory_id
        - client_id
        - filter

        Output:
        - total customers
        - total staff absence
        - absence percentage    
    
        """
        data = surveillance_ns.payload
        try:
            req_fields = ["factory_id", "client_id"] # leaving array here incase multiple fields are required in the future 
            for field in req_fields:
                if field not in data or not data[field.strip()]:
                    return {"message": f"{field} is missing."}, 400
                
            #----------- Validate formats with a loop -------------------------------
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400
                
            resp = surveillanceController.analytics_report(data)
            return {"message": "Monitoring Field data fetched successfully.",
                    "success": True,
                    "data": resp    
            },200
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})



# ---------------------------- Hourly Customer Traffic API ----------------------------
@surveillance_ns.route("/get_hourly_customer_traffic")
@surveillance_ns.doc("Get Hourly Customer Traffic")
class HourlyCustomerTraffic(Resource):
    @surveillance_ns.expect(hourly_customer_traffic_payload)
    @surveillance_ns.response(200, "Hourly customer traffic fetched successfully.")
    @surveillance_ns.response(400, "Validation Error")
    def put(self):
        """
        Shows Hourly Customer Traffic:
        Input:
        - client_id
        - factory_id
        - filters (date/week/month)
        Output:
        - hourly traffic data
        - total customers
        """
        data = surveillance_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
                
            # Validate date formats
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400

            results = surveillanceController.get_hourly_customer_traffic(data)
            return {"message": "Hourly customer traffic fetched successfully.", "data": results}, 200

        except Exception as e:
            print(f"Error in hourly_customer_traffic: {e}")
            abort(400, {"success": False, "message": f"Error {e}"})



# ---------------------------- Staff Presence Timeline API ----------------------------
@surveillance_ns.route("/get_staff_presence_timeline")
@surveillance_ns.doc("Get Staff Presence Timeline")
class StaffPresenceTimeline(Resource):
    @surveillance_ns.expect(staff_presence_timeline_payload)
    @surveillance_ns.response(200, "Staff presence timeline fetched successfully.")
    @surveillance_ns.response(400, "Validation Error")
    def put(self):
        """
        Shows Staff Presence Timeline:
        Input:
        - factory_id
        - filters (date)
        Output:
        - timeline entries with presence/absence durations
        """
        data = surveillance_ns.payload
        try:
            if "factory_id" not in data or not data["factory_id"]:
                return {"message": "factory_id is missing."}, 400

            # Validate date format
            f = data.get("filters", {})
            if f.get("date") and not re.match(r"\d{4}-\d{2}-\d{2}", f["date"]):
                return {"message": "Invalid date format"}, 400

            results = surveillanceController.get_staff_presence_timeline(data)
            return {"message": "Staff presence timeline fetched successfully.", "data": results}, 200

        except Exception as e:
            print(f"Error in staff_presence_timeline: {e}")
            abort(400, {"success": False, "message": f"Error {e}"})
