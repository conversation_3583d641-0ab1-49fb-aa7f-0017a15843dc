from flask_socketio import emit, join_room, leave_room
from api import socketio
from api.controllers import annotationController as annotate_func

@socketio.on('connect')
def handle_connect():
    print('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('join_dataset_room')
def handle_join_room(data):
    room = f"dataset_{data['client_id']}_{data['factory_id']}_{data['batch_id']}"
    join_room(room)
    emit('joined_room', {'room': room})

@socketio.on('leave_dataset_room')
def handle_leave_room(data):
    room = f"dataset_{data['client_id']}_{data['factory_id']}_{data['batch_id']}"
    leave_room(room)
    emit('left_room', {'room': room})

# WebSocket event to trigger dataset generation
@socketio.on('generate_dataset')
def handle_generate_dataset(data):
    try:
        req_fields = ["client_id", "factory_id", "batch_id"]
        for field in req_fields:
            if not data.get(field):
                emit('dataset_progress', {
                    'percentage': 0,
                    'message': f"{field} is missing.",
                    'stage': 'error'
                })
                return
        
        # Join the specific room for this dataset generation
        room = f"dataset_{data['client_id']}_{data['factory_id']}_{data['batch_id']}"
        join_room(room)
        
        # Start async dataset generation (progress will be emitted to room)
        annotate_func.generate_dataset_async(data)
        emit('dataset_progress', {
            'percentage': 1,
            'message': 'Dataset generation started.',
            'stage': 'initializing'
        })
    except Exception as e:
        emit('dataset_progress', {
            'percentage': 0,
            'message': f'Error: {str(e)}',
            'stage': 'error'
        })