from api import db
import datetime
from werkzeug.security import generate_password_hash
from sqlalchemy.orm import relationship
from sqlalchemy import <PERSON><PERSON><PERSON>

import json
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.orm import joinedload
from datetime import timedelta
from api.models.assetModels import *

# EmailNotification Model
class EmailNotification(db.Model):
    __tablename__ = "rpt_email_notifications"
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("uam_user.user_id"))
    areas = db.Column(db.ARRAY(db.Integer)) 
    sub_areas = db.Column(db.ARRAY(db.Integer)) 
    alert_emails = db.Column(JSONB)  
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.<PERSON>)
    
    @classmethod
    def add_email_notification(cls, user_id, data):
        """Add email notifications settings for the user."""
        new_email_notif = cls(
            user_id=user_id,
            areas=data["areas"],
            sub_areas=data["sub_areas"],
            alert_emails={"alert_emails": data["alert_emails"]},
            active=True
        )
        db.session.add(new_email_notif)
        db.session.commit()
        return new_email_notif
    
    @classmethod
    def update_email_notification(cls, user_id, data):
        """Update Email notifications settings for the user."""
        existing_notification = cls.query.filter_by(user_id=user_id).first()

        if existing_notification:
            existing_notification.areas = data.get("areas", existing_notification.areas)
            existing_notification.sub_areas = data.get("sub_areas", existing_notification.sub_areas)
            existing_notification.alert_emails = {"alert_emails": data.get("alert_emails", existing_notification.alert_emails)}
            existing_notification.updated_at = datetime.datetime.utcnow()
            flag_modified(existing_notification, "alert_emails")
            existing_notification.active= True
            db.session.commit() 

            return existing_notification
        else:
            # If no existing notification is found, create a new one
            new_email_notif = cls(
                user_id=user_id,
                areas=data.get("areas", []),
                sub_areas=data.get("sub_areas", []),
                alert_emails={"alert_emails": data.get("alert_emails", [])},
                active=True
            )
            db.session.add(new_email_notif)
            db.session.commit()

            return new_email_notif
    
    @classmethod
    def deactivate_email_notification(cls, user_id):
        """Deactivate (set active to False) Email notification settings for the user."""
        existing_notification = cls.query.filter_by(user_id=user_id).first()
        if existing_notification:
            existing_notification.active = False
            existing_notification.updated_at = datetime.datetime.utcnow()
            
            db.session.commit()
            
            return existing_notification
        else:
            return None



# WhatsAppNotification Model
class WhatsAppNotification(db.Model):
    __tablename__ = "rpt_whatsapp_notifications"
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("uam_user.user_id"))
    areas = db.Column(db.ARRAY(db.Integer)) 
    sub_areas = db.Column(db.ARRAY(db.Integer)) 
    phone_numbers = db.Column(JSONB) 
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean)
    
    @classmethod
    def add_whatsapp_notification(cls, user_id, data):
        """Add WhatsApp notifications settings for the user."""
        new_whatsapp_notif = WhatsAppNotification(
            user_id=user_id,
            areas=data["areas"],
            sub_areas=data["sub_areas"],
            phone_numbers={"phone_numbers" :data["phone_numbers"]},
            active=True
        )
        db.session.add(new_whatsapp_notif)
        db.session.commit()
        return new_whatsapp_notif
    
    
    @classmethod
    def update_whatsapp_notification(cls, user_id, data):
        """Update WhatsApp notifications settings for the user."""
        
        existing_notification = cls.query.filter_by(user_id=user_id).first()

        if existing_notification:

            existing_notification.areas = data.get("areas", existing_notification.areas)
            existing_notification.sub_areas = data.get("sub_areas", existing_notification.sub_areas)
            existing_notification.phone_numbers = {"phone_numbers": data.get("phone_numbers", existing_notification.phone_numbers)}
            flag_modified(existing_notification, "phone_numbers")
            existing_notification.updated_at = datetime.datetime.utcnow()
            existing_notification.active= True
            db.session.commit() 

            return existing_notification
        else:
            # If no existing notification is found, create a new one
            new_whatsapp_notif = cls(
                user_id=user_id,
                areas=data.get("areas", []),
                sub_areas=data.get("sub_areas", []),
                phone_numbers={"phone_numbers": data.get("phone_numbers", [])},
                active=True
            )
            db.session.add(new_whatsapp_notif)
            db.session.commit()

            return new_whatsapp_notif
    
    
    @classmethod
    def deactivate_whatsapp_notification(cls, user_id):
        """Deactivate (set active to False) WhatsApp notification settings for the user."""
        
        existing_notification = cls.query.filter_by(user_id=user_id).first()

        if existing_notification:
            existing_notification.active = False
            existing_notification.updated_at = datetime.datetime.utcnow()
            db.session.commit() 
            return existing_notification
        else:
            return None
        

class Tickets(db.Model):
    __tablename__ = 'rpt_tickets'
    __table_args__ = (
        db.Index('idx_user_client_factory', 'user_id', 'client_id', 'factory_id'),
    )
    ticket_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('uam_user.user_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    priority = db.Column(db.Text, nullable=False, default="low")  
    description = db.Column(db.Text, nullable=True)  
    status = db.Column(db.Text, nullable=False, default="open")  
    comment = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)

    def to_dict(self):
        return {
            "ticket_id": self.ticket_id,
            "user_id": self.user_id,
            "client_id": self.client_id,
            "client_name": Client.get_name_by_id(self.client_id),
            "factory_id": self.factory_id,
            "factory_name": Factory.get_name_by_id(self.factory_id),
            "priority": self.priority,
            "query": self.description,
            "status": self.status,
            "comment": self.comment,
            "created_at": self.created_at.strftime("%Y-%m-%d %I:%M %p"),
            "updated_at": self.updated_at.strftime("%Y-%m-%d %I:%M %p")
        }

    @classmethod
    def get_by_user_client_factory_id(cls, user_id, client_id, factory_id):
        tickets = cls.query.filter_by(user_id=user_id, client_id=client_id, factory_id=factory_id, active=True).all()
        return [ticket.to_dict() for ticket in tickets]
    
    @classmethod
    def get_by_client_factory_id(cls, client_id, factory_id):
        tickets = cls.query.filter_by(client_id=client_id, factory_id=factory_id, active=True).all()
        return [ticket.to_dict() for ticket in tickets]
            
    @classmethod
    def get_by_id(cls, ticket_id):
        ticket = cls.query.filter(cls.ticket_id == ticket_id, cls.active == True).first()
        if ticket:
            return ticket
        else:
            return None
    
    @classmethod
    def get_all_records(cls):
        tickets = cls.query.filter_by(active=True).all()
        return [ticket.to_dict() for ticket in tickets]

    @classmethod
    def create_update(cls, data):
        ticket_id = data.get("ticket_id")
        if ticket_id:
            ticket = Tickets.get_by_id(ticket_id)
            if ticket:
                # Update fields
                ticket.user_id = ticket.user_id
                ticket.client_id = ticket.client_id
                ticket.factory_id = ticket.factory_id
                ticket.priority = data.get("priority", ticket.priority).lower() if data.get("priority") else ticket.priority
                ticket.description = data.get("query", ticket.description)
                ticket.status = data.get("status", ticket.status)
                ticket.comment = data.get("comment", ticket.comment)
                ticket.updated_at = datetime.datetime.utcnow()
                db.session.commit()
                return ticket, "updated"
        # Create new
        new_rec = cls(
            user_id=data["generated_by"],
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            priority=data["priority"].lower(),
            description=data["query"],
            status="open",
            comment=data.get("comment"),
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec, "added"
            

    @classmethod
    def toggle_status(cls, data):
        ticket = Tickets.get_by_id(data["ticket_id"])
        if ticket:
            ticket.status = data["status"].lower()
            ticket.comment = data["comment"]
            ticket.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return ticket
        else:
            return None