from api import import_ns

from flask_restx import fields



import_cameras_payload = import_ns.model(
    "Import Cameras",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="The associated client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated factory ID"
        ),
        "import_url": fields.String(
            default="https://",
            required=True,
            description="Import URL, from where cameras will be imported",
        ),
    },
)