from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import func

class Client(db.Model):
    __tablename__ = 'ast_client'

    client_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    logo = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # New column: store timezone as string ("Asia/Karachi", "Europe/London", etc.)
    timezone = db.Column(db.Text, nullable=False, default="UTC")
    
    @classmethod
    def get_by_id(cls, client_id):
        return cls.query.filter_by(client_id=client_id, active=True).first()

    @classmethod
    def get_name_by_id(cls, client_id):
        rec = cls.query.with_entities(cls.name).filter(cls.client_id==client_id, cls.active==True).first()
        return rec.name
        
    @classmethod
    def get_timezone_by_id(cls, client_id):
        rec = cls.query.with_entities(cls.timezone).filter(cls.client_id==client_id, cls.active==True).first()
        return rec.timezone
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.client_id.in_(id_list)).all()
        if find_record:
            final_list= [x.client_id for x in find_record if x.active]
        return final_list

    @classmethod
    def get_records_by_list(cls,id_list):
        return cls.query.filter(cls.client_id.in_(id_list),cls.active==True).all()
       

    @classmethod
    def create(cls, data):
        new_rec = Client(
            name=data["name"],
            address=data["address"],
            logo=data["logo"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self


    @classmethod
    def toggle_status(cls, client_id):
        rec = cls.query.filter_by(client_id=client_id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None
        

class Factory(db.Model):
    __tablename__ = 'ast_factory'

    factory_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    #Relationships
    users_factories = relationship('UserFactory', backref='factory')
    factory_areas = relationship('Area', backref='factory')


    @classmethod
    def get_by_id(cls, factory_id):
        return cls.query.filter_by(factory_id=factory_id, active=True).first() 

    @classmethod
    def get_records_by_client_id(cls,client_id):
        return cls.query.filter(cls.client_id==client_id,cls.active==True).all()
        
    @classmethod
    def get_name_by_id(cls, factory_id):
        rec = cls.query.with_entities(cls.name).filter(cls.factory_id==factory_id, cls.active==True).first()
        return rec.name
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.factory_id.in_(id_list)).all()
        if find_record:
            final_list= [x.factory_id for x in find_record if x.active]
        return final_list

    @classmethod
    def get_records_by_list(cls,id_list):
        return cls.query.filter(cls.factory_id.in_(id_list),cls.active==True).all()
       

    @classmethod
    def create(cls, data):
        new_rec = Factory(
            client_id=data["client_id"],
            name=data["name"],
            address=data["address"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
        
            for i in self.users_factories:
                db.session.delete(i)
                
            for area in self.factory_areas:
                
                for sub_area in area.sub_areas:
                    for camera in sub_area.cameras:
                        db.session.delete(camera)
                    db.session.delete(sub_area)
                
                for user_area in area.users_areas:
                    db.session.delete(user_area)
                    
                db.session.delete(area)
                    
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, factory_id):
        rec = cls.query.filter_by(factory_id=factory_id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None


class Area(db.Model):
    __tablename__ = 'ast_area'

    area_id = db.Column(db.Integer, primary_key=True)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    
    users_areas = relationship('UserArea', backref='area')
    sub_areas = relationship('SubArea', backref='area')


    @classmethod
    def get_by_id(cls, area_id):
        return cls.query.filter_by(area_id=area_id, active=True).first()    
    
    @classmethod
    def get_name_by_area_id(cls, area_id):
        return cls.query.with_entities(cls.name).filter_by(area_id=area_id).first()
    
    @classmethod
    def get_area_id_by_name(cls, name):
        return cls.query.with_entities(cls.area_id).filter_by(name=name).first()

    @classmethod
    def get_area_ids_by_name_list(cls, area_names, factory_id):
        return cls.query.with_entities(cls.area_id).filter(cls.name.in_(area_names),cls.factory_id==factory_id).all()
    
    @classmethod
    def get_dict_of_area_ids_by_name_list(cls, area_names, factory_id):
        result = cls.query.with_entities(cls.name, cls.area_id).filter(
            cls.name.in_(area_names),
            cls.factory_id == factory_id
        ).all()
        return {name: area_id for name, area_id in result}

    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_total_records(cls):
        return cls.query.all()
    
    @classmethod
    def get_records_by_list(cls,ids_list):
        return cls.query.filter(cls.area_id.in_(ids_list), cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_ids(cls,ids_list):
        return cls.query.filter(cls.factory_id.in_(ids_list), cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_id(cls,factory_id):
        return cls.query.filter(cls.factory_id ==factory_id , cls.active==True).all()
    
    @classmethod
    def get_records_by_factory_and_area(cls,area_names,factory_id):
        print("names: ", area_names) 
        return cls.query.with_entities(cls.area_id).filter(cls.factory_id ==factory_id , cls.active==True, cls.name.in_(area_names)).all() 
    
    @classmethod
    def get_records_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
    
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.area_id.in_(id_list)).all()
        if find_record:
            final_list= [x.area_id for x in find_record if x.active]
        return final_list
    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            name=data["name"],
            address=data["address"],
            factory_id=data["factory_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    def update(self, name=None, address=None, factory_id=None):
        if name:
            self.name = name
        if address:
            self.address = address
        if self.factory_id:
            self.factory_id= factory_id
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
            for i in self.users_areas:
                db.session.delete(i)
                
            for sub_area in self.sub_areas:
                for camera in sub_area.cameras:
                    db.session.delete(camera)
                db.session.delete(sub_area)
                
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(area_id=id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None




            
class SubArea(db.Model):
    __tablename__ = 'ast_subarea'

    sub_area_id = db.Column(db.Integer, primary_key=True)
    area_id = db.Column(db.Integer, db.ForeignKey('ast_area.area_id'))
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    name = db.Column(db.Text, nullable=False)
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    

    @classmethod
    def get_by_id(cls, sub_area_id):
        return cls.query.filter_by(sub_area_id=sub_area_id, active=True).first()    
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_records_by_list(cls,ids_list):
        return cls.query.filter(cls.sub_area_id.in_(ids_list),cls.active == True).all()
    
    @classmethod
    def get_records_by_area_list(cls,area_ids):
        return cls.query.filter(cls.area_id.in_(area_ids),cls.active == True).all()
    
    @classmethod
    def get_records_by_area_id(cls,area_id): 
        return cls.query.filter(cls.area_id == area_id,cls.active == True).all()
    
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.sub_area_id.in_(id_list)).all()
        if find_record:
            final_list= [x.sub_area_id for x in find_record if x.active]
        return final_list
    
    @classmethod
    def get_records_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()

    
    @classmethod
    def create(cls, data):
        new_rec = cls(
            name=data["name"],
            address=data["address"],
            area_id=data["area_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    @classmethod
    def create_list(cls, data):
        result=[]
        for area in data["areas"]:
            for sub in area["sub_areas"]:
                result.append(cls(
                    name=sub,
                    area_id=area["area_id"],
                    active=True,
                    created_at=datetime.datetime.utcnow()
                ))
            
        db.session.add_all(result)
        db.session.commit()
        return True


    def update(self, name=None,  address=None):
        if name:
            self.name = name
        if address:
            self.address = address
                        
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
            for camera in self.cameras:
                db.session.delete(camera)
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(sub_area_id=id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None
        


class Cameras(db.Model):
    __tablename__ = 'ast_cameras'
    camera_id = db.Column(db.Text, primary_key=True, unique=True)
    client_id = db.Column(db.Integer,  db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer,  db.ForeignKey('ast_factory.factory_id'))
    area_id = db.Column(db.Integer,  db.ForeignKey('ast_area.area_id'))
    sub_area_id = db.Column(db.Integer, db.ForeignKey('ast_subarea.sub_area_id'))
    
    camera_ip = db.Column(db.Text, nullable=False)
    camera_name = db.Column(db.Text, )
    camera_position_no = db.Column(db.Text,)
    
    nvr_no = db.Column(db.Integer)
    username = db.Column(db.Text)
    password = db.Column(db.Text)
    stream = db.Column(db.Text)
    port = db.Column(db.Integer)
   
    modules = db.Column(ARRAY(db.Integer))
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    @classmethod
    def get_by_id(cls, camera_id):
        return cls.query.filter_by(camera_id=camera_id, active=True).first()
        
    @classmethod
    def get_by_ip(cls, camera_ip):
            return cls.query.filter_by(camera_ip=camera_ip, active=True).first()
       
    @classmethod
    def get_active_camera_ids(cls):
        return cls.query.with_entities(cls.camera_id).filter_by(active=True).all()

    @classmethod
    def get_name_by_camera_id(cls, camera_id):
        return cls.query.with_entities(cls.camera_name).filter_by(camera_id=camera_id, active=True).first()

    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_active_inactive_records(cls):
        return cls.query.all()
    
    @classmethod
    def get_records_by_area_list(cls,area):
        return cls.query.filter(cls.sub_area_id.in_(area)).all()
    
    @classmethod
    def get_records_module_wise(cls): 
        return cls.query(
            func.unnest(Cameras.modules).label('module_id'),
            func.count().label('camera_count')).group_by('module_id').order_by('module_id').all()
    
    @classmethod
    def get_records_by_sub_area_list(cls,sub_areas_ids):
        return cls.query.filter(cls.sub_area_id.in_(sub_areas_ids), cls.active==True).all() 

    @classmethod
    def get_modules_by_cameraID(cls, cameraID):
        result = cls.query.with_entities(cls.modules).filter_by(camera_id=cameraID).first()
        modules_list = []
        if result:
            modules_list = result.modules

        return modules_list

    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id, active=True).all()
    
    @classmethod
    def create(cls, data, modules):
        new_rec = cls(
            camera_id=data["camera_id"].strip(),
            camera_ip=data["camera_ip"],
            camera_name=data["camera_name"],
            camera_position_no=data["camera_position_no"],
            nvr_no=data["nvr_no"],
            username=data["username"],
            password=data["password"],
            stream=data["stream"],
            port=data["port"],
            modules=modules,
            client_id=data["client_id"],
            factory_id=data["factory_id"],
            area_id=data["area_id"],
            sub_area_id=data["sub_area_id"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec

    @classmethod
    def create_bulk(cls, cameras_list, status=None):
        add_cameras=[]
        for data in cameras_list:
            camera_id = data["camera_id"].strip()

            # Try to find an existing camera by camera_id
            existing_camera = cls.query.filter_by(camera_id=camera_id).first()

            if existing_camera:
                # Update fields
                existing_camera.camera_ip = data["camera_ip"]
                existing_camera.camera_name = data["camera_name"]
                existing_camera.camera_position_no = data["camera_position_no"]
                existing_camera.nvr_no = data["nvr_no"]
                existing_camera.username = data["username"]
                existing_camera.password = data["password"]
                existing_camera.stream = data["stream"]
                existing_camera.port = data["port"]
                existing_camera.modules = data["modules"]
                existing_camera.client_id = data["client_id"]
                existing_camera.factory_id = data["factory_id"]
                existing_camera.area_id = data["area_id"]
                existing_camera.sub_area_id = data["sub_area_id"]
                existing_camera.updated_at = datetime.datetime.utcnow()
                existing_camera.active = True
                db.session.commit()
            else:
                add_cameras.append(cls(
                    camera_id=camera_id,
                    camera_ip=data["camera_ip"],
                    camera_name=data["camera_name"],
                    camera_position_no=data["camera_position_no"],
                    nvr_no=data["nvr_no"],
                    username=data["username"],
                    password=data["password"],
                    stream=data["stream"],
                    port=data["port"],
                    modules=data["modules"],
                    client_id=data["client_id"],
                    factory_id=data["factory_id"],
                    area_id=data["area_id"],
                    sub_area_id=data["sub_area_id"],
                    active=True,
                ))
        db.session.add_all(add_cameras)
        db.session.commit()
        
        
    def update(self, data, modules):
        if data["change_camera_id"]:
            self.camera_id = data["new_camera_id"]
        if "camera_ip" in data and data["camera_ip"]:
            self.camera_ip = data["camera_ip"]
        if "modules" in data and len(modules) > 0:
            self.modules = modules
        if "sub_area_id" in data and data["sub_area_id"]:
            self.sub_area_id = data["sub_area_id"]
        if "camera_name" in data and data["camera_name"]:
            self.camera_name = data["camera_name"]
        if "camera_position_no" in data and data["camera_position_no"]:
            self.camera_position_no = data["camera_position_no"]
        if "nvr_no" in data and data["nvr_no"]:
            self.nvr_no = data["nvr_no"]
        if "username" in data and data["username"]:
            self.username = data["username"]
        if "password" in data and data["password"]:
            self.password = data["password"]
        if "stream" in data and data["stream"]:
            self.stream = data["stream"]
        if "port" in data and data["port"]:
            self.port = data["port"]
        self.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return self

    def delete(self):
        if self.image:
            self.image.delete()
            
        db.session.delete(self)
        db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(camera_id=id).first()
        if rec:
            rec.active = not rec.active
            db.session.commit()
            return rec
        return None
    

class CamerasliveFeed(db.Model):
    __tablename__ = 'ast_camera_livefeed'
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    image_url = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    @classmethod
    def create_update(cls, data):
        rows = []
        try:
            updated = False
            for item in data:
                client_id = item.get("client_id")
                factory_id = item.get("factory_id")
                camera_id = item.get("camera_id")
                image_url = item.get("image_url")
                timestamp = item.get("timestamp")
                print("Processing:", camera_id, client_id, factory_id)
                existing_record = cls.query.filter(
                    (cls.camera_id == camera_id) &
                    (cls.client_id == client_id) &
                    (cls.factory_id == factory_id)
                ).first()
                print("Existing record:", existing_record)
                if existing_record:
                    existing_record.image_url = image_url
                    existing_record.timestamp = timestamp
                    updated = True
                else:
                    new_record = cls(
                        client_id=client_id,
                        factory_id=factory_id,
                        camera_id=camera_id,
                        image_url=image_url,
                        timestamp=timestamp
                    )
                    rows.append(new_record)

            if rows:
                db.session.add_all(rows)
            if rows or updated:
                db.session.commit()

        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error Uploading camera livefeed: {e}")

            
    
    @classmethod
    def get_by_camera_id(cls, camera_id, client_id, factory_id):
        return cls.query.filter_by(camera_id=camera_id , client_id=client_id, factory_id=factory_id).first()
    

class AstZones(db.Model):
    __tablename__ = 'ast_zone'
    zone_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    name = db.Column(db.Text, nullable=True)
    gps_cordinates = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    @classmethod
    def get_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
    
    @classmethod
    def create_update(cls, data):
        rows = []
        try:
            for item in data:
                client_id = item.get("client_id")
                factory_id = item.get("factory_id")
                name = item.get("name")
                gps_cordinates = item.get("gps_cordinates")
                active=item.get("active")
                
                existing_record = cls.query.filter((cls.name == name)&(cls.client_id == client_id)&(cls.factory_id == factory_id)).first()

                if existing_record:
                    existing_record.gps_cordinates=gps_cordinates
                    existing_record.active=active
                else:
                    # Create a new record
                    new_record = cls(
                        client_id=client_id,
                        factory_id=factory_id,
                        name=name,
                        gps_cordinates=gps_cordinates, 
                        active=active  
                    )
                    rows.append(new_record)

            if rows:
                db.session.add_all(rows)
                db.session.commit()
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error AstZones->create_update: {e}")
        

    
class Frontend_Menu(db.Model):
    __tablename__ = 'ast_frontend_menu'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('ast_products.product_id'), nullable=False)
    name = db.Column(db.Text, nullable=False)
    url = db.Column(db.Text, nullable=False)
    icon = db.Column(db.Text, nullable=True)
    order = db.Column(db.Integer, nullable=False)
    product_id = db.Column(db.Integer, nullable=True)
    active = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    
    @classmethod
    def get_active_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id, active=True).first()
    
    @classmethod
    def get_by_parent_id(cls, product_id):
        return cls.query.filter_by(product_id=product_id, active=True).all()
    
    @classmethod
    def createUpdate(cls, data):
        """
        Create or update a menu item.
        
        Args:
            data (dict): The data for the menu item
            
        Returns:
            tuple: (menu_instance, created) where created is boolean
        """
        # Try to find existing record
        menu_item = cls.query.filter_by(
            id=data["id"]
        ).first()

        created = False
        
        if menu_item:
            # Update existing record
            for key, value in data.items():
                if hasattr(menu_item, key):
                    setattr(menu_item, key, value)
        else:
            # Create new record
            menu_item = cls(**data)
            db.session.add(menu_item)
            created = True
        
        try:
            db.session.commit()
            return menu_item, created
        except Exception as e:
            db.session.rollback()
            raise e
    

class Factory_Frontend_Menu(db.Model):
    __tablename__ = 'ast_factory_frontend_menu'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    menu_id = db.Column(db.Integer, db.ForeignKey('ast_frontend_menu.id'), nullable=False)

    @classmethod
    def get_menu_by_factory_id(cls, factory_id):
        results = db.session.query(
            cls.menu_id,
            Frontend_Menu.name,
            Frontend_Menu.url,
            Frontend_Menu.icon,
            Frontend_Menu.order,
            Frontend_Menu.product_id
        ).join(
            Frontend_Menu, cls.menu_id == Frontend_Menu.id
        ).filter(
            cls.factory_id == factory_id,
            Frontend_Menu.active == True 
        ).all()
        
        return [
            {
                "menu_id": result.menu_id,
                "name": result.name,
                "url": result.url,
                "icon": result.icon,
                "order": result.order,
                "parent_id": result.product_id
            }
            for result in results
        ]
    
    @classmethod
    def createUpdate(cls, factory_id, menu_id, **kwargs):
        """
        Create or update a factory-menu association.
        
        Args:
            factory_id (int): The ID of the factory
            menu_id (int): The ID of the menu
            **kwargs: Additional attributes to update if record exists
            
        Returns:
            tuple: (factory_menu_instance, created) where created is boolean
        """
        # Try to find existing record
        factory_menu = cls.query.filter_by(
            factory_id=factory_id,
            menu_id=menu_id
        ).first()

        created = False
        
        if factory_menu:
            # Update existing record
            for key, value in kwargs.items():
                if hasattr(factory_menu, key):
                    setattr(factory_menu, key, value)
        else:
            # Create new record
            factory_menu = cls(
                factory_id=factory_id,
                menu_id=menu_id,
                **kwargs
            )
            db.session.add(factory_menu)
            created = True
        
        try:
            db.session.commit()
            return factory_menu, created
        except Exception as e:
            db.session.rollback()
            raise e


class Products(db.Model):
    __tablename__ = 'ast_products'

    product_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)

    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_by_id(cls, product_id):
        return cls.query.filter_by(product_id=product_id, active=True).first()
    
    @classmethod
    def get_name_by_id(cls, product_id):
        rec = cls.query.with_entities(cls.name).filter(cls.product_id==product_id, cls.active==True).first()
        return rec.name if rec else None
    
    @classmethod
    def createUpdate(cls, data):
        """
        Create or update a product.
        
        Args:
            data (dict): The data for the product
            
        Returns:
            tuple: (product_instance, created) where created is boolean
        """
        # Try to find existing record
        product = cls.query.filter_by(
            product_id=data.get("product_id")
        ).first()

        created = False
        
        if product:
            # Update existing record
            for key, value in data.items():
                if hasattr(product, key):
                    setattr(product, key, value)
            product.updated_at = datetime.datetime.utcnow()
        else:
            # Create new record
            product = cls(**data)
            db.session.add(product)
            created = True
        
        try:
            db.session.commit()
            return product, created
        except Exception as e:
            db.session.rollback()
            raise e
    
    @classmethod
    def toggle_status(cls, product_id):
        rec = cls.query.filter_by(product_id=product_id).first()
        if rec:
            rec.active = not rec.active
            rec.updated_at = datetime.datetime.utcnow()
            db.session.commit()
            return rec
        return None


class Factory_Products(db.Model):
    __tablename__ = 'ast_factory_products'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('ast_products.product_id'), nullable=False)

    @classmethod
    def get_products_by_factory_id(cls, factory_id):
        results = db.session.query(
            cls.product_id,
            Products.name
        ).join(
            Products, cls.product_id == Products.product_id
        ).filter(
            cls.factory_id == factory_id,
            Products.active == True 
        ).all()
        
        return [
            {
                "product_id": result.product_id,
                "name": result.name
            }
            for result in results
        ]
    
    @classmethod
    def createUpdate(cls, factory_id, product_id, **kwargs):
        """
        Create or update a factory-product association.
        
        Args:
            factory_id (int): The ID of the factory
            product_id (int): The ID of the product
            **kwargs: Additional attributes to update if record exists
            
        Returns:
            tuple: (factory_product_instance, created) where created is boolean
        """
        # Try to find existing record
        factory_product = cls.query.filter_by(
            factory_id=factory_id,
            product_id=product_id
        ).first()

        created = False
        
        if factory_product:
            # Update existing record
            for key, value in kwargs.items():
                if hasattr(factory_product, key):
                    setattr(factory_product, key, value)
        else:
            # Create new record
            factory_product = cls(
                factory_id=factory_id,
                product_id=product_id,
                **kwargs
            )
            db.session.add(factory_product)
            created = True
        
        try:
            db.session.commit()
            return factory_product, created
        except Exception as e:
            db.session.rollback()
            raise e


class AstDevices(db.Model):
    __tablename__ = 'ast_device'
    
    __table_args__ = (
        # device_id must be unique inside each (client, factory)
        db.UniqueConstraint('client_id', 'factory_id', 'device_id', name='uq_device_per_factory'),
    )
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)

    # sequential inside each (client, factory)
    device_id = db.Column(db.Integer, nullable=False)

    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))

    key = db.Column(db.Text, nullable=False)
    secret = db.Column(db.Text, nullable=False)

    name = db.Column(db.Text, nullable=True)
    location = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.Boolean, default=True, nullable=False)
    
    @classmethod
    def get_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
    
    @classmethod
    def create_update(cls, data):
        rows = []
        try:
            for item in data:
                client_id = item.get("client_id")
                factory_id = item.get("factory_id")
                name = item.get("name")
                location = item.get("location", "")
                active = item.get("active", True)

                # check if record already exists (by name+client+factory)
                existing_record = cls.query.filter(
                    (cls.name == name) &
                    (cls.client_id == client_id) &
                    (cls.factory_id == factory_id)
                ).first()

                if existing_record:
                    # update existing record
                    existing_record.location = location
                    existing_record.active = active
                else:
                    # find the next device_id in this factory
                    max_device_id = db.session.query(func.max(cls.device_id)) \
                        .filter_by(client_id=client_id, factory_id=factory_id) \
                        .scalar()

                    next_device_id = (max_device_id or 0) + 1

                    # create new record
                    new_record = cls(
                        client_id=client_id,
                        factory_id=factory_id,
                        device_id=next_device_id,
                        name=name,
                        location=location,
                        active=active
                    )
                    rows.append(new_record)

            if rows:
                db.session.add_all(rows)
            
            db.session.commit()
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error AstDevices->create_update: {e}")


class AstVideos(db.Model):
    __tablename__ = 'ast_videos'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    name = db.Column(db.Text, nullable=False)
    description = db.Column(db.Text, nullable=True)
    video_url = db.Column(db.Text, nullable=False)
    modules = db.Column(ARRAY(db.Integer))
    duration = db.Column(db.Integer, nullable=False)
    size = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Text, nullable=False)
    sha256sum = db.Column(db.Text, nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()

    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
    
    @classmethod
    def get_by_camera_id(cls, camera_id):
        return cls.query.filter_by(camera_id=camera_id).first() 
    
    @classmethod
    def get_records_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
    
    @classmethod
    def create(cls, data):
        try:
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")
            camera_id = data.get("camera_id")
            name = data.get("name")
            description = data.get("description")
            video_url = data.get("video_url")
            duration = data.get("duration")
            size = data.get("size")
            modules = data.get("modules")
            status = data.get("status")
            sha256sum = data.get("sha256sum")

            new_record = cls(
                client_id=client_id,
                factory_id=factory_id,
                camera_id=camera_id,
                name=name,
                description=description,
                video_url=video_url,
                duration=duration,
                size=size,
                modules=modules,
                status=status,
                sha256sum=sha256sum
            )

            db.session.add(new_record)
            db.session.commit()

            return new_record
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error AstVideos->create_update: {e}")


    @classmethod
    def status_update(cls, data):
        try:
            id = data.get("id")
            status = data.get("status")

            existing_record = cls.query.filter_by(id=id).first()

            if existing_record:
                existing_record.status = status

            db.session.commit()
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error AstVideos->status_update: {e}")



class AstCameraROI(db.Model):
    __tablename__ = 'ast_camera_roi'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'), nullable=False)
    roi = db.Column(db.Text, nullable=True)
    module_id  = db.Column(db.Integer, nullable=True)
    color  = db.Column(db.Text, nullable=True)
    start_time = db.Column(db.Time, nullable=True)
    end_time  = db.Column(db.Time, nullable=True) 
    no_of_person  = db.Column(db.Integer, nullable=True)
    frequency_time  = db.Column(db.Integer, nullable=True)
    alert_frequency  = db.Column(db.Integer, nullable=True)
    level  = db.Column(db.Integer, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
    
    @classmethod
    def get_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()

    @classmethod
    def get_by_camera_id(cls, camera_id):
        return cls.query.filter_by(camera_id=camera_id).first()
    
    
    @classmethod
    def createUpdate(cls, data):
        try:
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")
            camera_id = data.get("camera_id")
            roi = data.get("roi")
            module_id = data.get("module_id")
            color = data.get("color")
            start_time = data.get("start_time")
            end_time = data.get("end_time")
            no_of_person = data.get("no_of_person")
            frequency_time = data.get("frequency_time")
            alert_frequency = data.get("alert_frequency")
            level = data.get("level")

            existing_record = cls.query.filter(
                (cls.client_id == client_id) &
                (cls.factory_id == factory_id) &
                (cls.camera_id == camera_id)
            ).first()   
            
            if existing_record:
                existing_record.roi = roi
                existing_record.module_id = module_id
                existing_record.color = color
                existing_record.start_time = start_time
                existing_record.end_time = end_time
                existing_record.no_of_person = no_of_person
                existing_record.frequency_time = frequency_time
                existing_record.alert_frequency = alert_frequency
                existing_record.level = level
                existing_record.updated_at = datetime.datetime.utcnow()
            else:
                new_record = cls(
                    client_id=client_id,
                    factory_id=factory_id,
                    camera_id=camera_id,
                    roi=roi,
                    module_id=module_id,
                    color=color,
                    start_time=start_time,
                    end_time=end_time,
                    no_of_person=no_of_person,
                    frequency_time=frequency_time,
                    alert_frequency=alert_frequency,
                    level=level,
                    created_at=datetime.datetime.utcnow(),
                    updated_at=datetime.datetime.utcnow(),
                )
                db.session.add(new_record)
            
            db.session.commit()
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error AstCameraROI->create: {e}")


class Shift(db.Model):
    __tablename__ = 'ast_shift'

    shift_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    name = db.Column(db.Text, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    @classmethod
    def get_all_records(cls):
        return cls.query.all()

    @classmethod
    def get_all_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
      
    @classmethod
    def get_by_id(cls, shift_id):
        return cls.query.filter_by(shift_id=shift_id).first()

    @classmethod
    def get_by_client_factory(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id, active=True).all()  

    @classmethod
    def createUpdate(cls, data):
        try:
            shift_id = data.get("shift_id")
            existing_record = cls.query.filter_by(shift_id=shift_id).first()

            if existing_record:
                existing_record.name = data.get("name")
                existing_record.start_time = data.get("start_time")
                existing_record.end_time = data.get("end_time")
                existing_record.active = data.get("active")
                existing_record.updated_at = datetime.datetime.utcnow()
            else:
                new_record = cls(
                    client_id=data.get("client_id"),
                    factory_id=data.get("factory_id"),
                    name=data.get("name"),
                    start_time=data.get("start_time"),
                    end_time=data.get("end_time"),
                    active=data.get("active"),
                    created_at=datetime.datetime.utcnow(),
                    updated_at=datetime.datetime.utcnow(),
                )
                db.session.add(new_record)
            
            db.session.commit()
            
            return existing_record or new_record
        
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Error Shift->create: {e}")