import calendar
import threading
import time
import requests
import os
import yaml

from collections import Counter
from PIL import Image as pil_Image
from collections import defaultdict

from api.utils.utils import *
from api.utils.gcp_storage import *
from api import db

from api.models.uamModels import *
from api.models.annotationModels import *
from api.models.assetModels import *
from api.models.hseModels import *

from sqlalchemy import and_, or_, func, case, extract
from sqlalchemy.dialects.postgresql import ARRAY

from datetime import datetime, timedelta
from collections import defaultdict
from sqlalchemy.sql import text
from api import socketio, app



def get_unannotated_images(data):
    try:
        user_id = data["user_id"]
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        limit = data["limit"]
        job_id = data["job_id"]

        unannotated_images = (
            db.session.query(AnnotationCameraImages)
            .filter(
                and_(
                    AnnotationCameraImages.annotation_status == False,
                    AnnotationCameraImages.disqualified == False,
                    AnnotationCameraImages.background_image == False,
                    AnnotationCameraImages.client_id == client_id,
                    AnnotationCameraImages.factory_id == factory_id,
                    AnnotationCameraImages.batch_id == batch_id,
                    AnnotationCameraImages.job_id == job_id,
                    AnnotationCameraImages.annotation_user_id == user_id
                )
            )
            .limit(5)
            .all()
        )

        annotated_images_count = AnnotationCameraImages.query.filter(
            AnnotationCameraImages.job_id == job_id,
            AnnotationCameraImages.annotation_status == True
        ).count()

        results = []
        if unannotated_images:
            for record in unannotated_images:
                results.append({
                    "id": record.id,
                    "camera_id": record.camera_id,
                    "image_url": record.image_url,
                    "annotation_user_id": record.annotation_user_id,
                    "annotation": record.annotation,
                    "rejected": record.rejected,
                    "review_status": record.review_status,
                    "review_comments": record.review_comments,
                    "detection_model": record.detection_model,
                    "modules": Modules.get_records_by_list(
                        AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)
                    ),
                    "job_id": record.job_id,
                    "limit": limit,
                    "created_at": record.created_at.strftime("%Y-%m-%d"),
                    "updated_at": record.updated_at.strftime("%Y-%m-%d")
                })

        return results, annotated_images_count

    except Exception as e:
        print(f"Error in get_unannotated_images: {e}")
        return {"error": str(e)}
    
    
def get_review_images(data):
    
    try:
        
        user_id = data["user_id"]
        annotation_user_id = data["annotation_user_id"]
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        limit = data["limit"]
        job_id = data["job_id"]
        
        
        review_images = (
            db.session.query(AnnotationCameraImages)
            .filter(
                and_(
                    AnnotationCameraImages.annotation_status == True,
                    AnnotationCameraImages.review_status == False,
                    AnnotationCameraImages.background_image == False,
                    AnnotationCameraImages.client_id == client_id,
                    AnnotationCameraImages.factory_id == factory_id,
                    AnnotationCameraImages.batch_id == batch_id,
                    AnnotationCameraImages.annotation_user_id == annotation_user_id,
                    AnnotationCameraImages.job_id == job_id,
                    AnnotationCameraImages.dataset_id == 0,
                    or_(
                        AnnotationCameraImages.review_user_id == 0,
                        AnnotationCameraImages.review_user_id == user_id
                    )
                )
            )
            .limit(5)
            .all()
        )


        reviewed_images_count = AnnotationCameraImages.query.filter(
            AnnotationCameraImages.job_id == job_id,
            AnnotationCameraImages.review_status == True
        ).count()

        results = []
        if len(review_images) > 0:
        
            # Update review_user_id and commit
            # And also make the result
            
            for record in review_images:
                record.review_user_id = user_id
                
                review_result =     {
                    "id": record.id,
                    "camera_id": record.camera_id,
                    "image_url": record.image_url,
                    "annotation_user_id": record.annotation_user_id,
                    "annotation_user_name": Users.get_name_by_user_id(record.annotation_user_id),
                    "annotation": record.annotation,
                    "disqualified": record.disqualified,
                    "detection_model": record.detection_model,
                    "background_image": record.background_image,
                    "modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)),
                    "job_id": record.job_id,
                    "limit": limit,
                    "created_at": record.created_at.strftime("%Y-%m-%d"),
                    "updated_at": record.updated_at.strftime("%Y-%m-%d")
                }
                
                results.append(review_result)

            db.session.commit()
            
            
        return results, reviewed_images_count
        
    except Exception as e:
        print(f"Error in get_review_images: {e}")
        return {"error": str(e)}


def get_annotation_summary(data):
    
    try:
        user_id = data["user_id"]
        
        records = db.session.query(
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id,
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.rejected == True).label('rejected_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.disqualified == True).label('disqualified_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == True).label('annotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == False,AnnotationCameraImages.disqualified == False).label('unannotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.background_image == True).label('background_images_count'),
        ).select_from(AnnotationCameraImages
        ).filter( 
            AnnotationCameraImages.dataset_id == 0
        ).group_by(
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id
        ).all() 

        results = []
        
        existing_client_factory_keys = set()
        existing_client_factory_batch_keys = set()

        for r in records:

            key = (r.client_id, r.factory_id)
            existing_client_factory_keys.add(key)

            key2 = (r.client_id, r.factory_id, r.batch_id)
            existing_client_factory_batch_keys.add(key2)

            client_name = Client.get_name_by_id(r.client_id)
            factory_name = Factory.get_name_by_id(r.factory_id)

            result = {
                "client_id": r.client_id,
                "client_name": client_name,
                "factory_id": r.factory_id,
                "factory_name": factory_name,
                "batch_id": r.batch_id,
                "annotated_images_count": r.annotated_images_count,
                "unannotated_images_count": r.unannotated_images_count,
                "rejected_images_count": r.rejected_images_count,
                "disqualified_images_count": r.disqualified_images_count,
                "background_images_count": r.background_images_count,
                "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(r.client_id, r.factory_id, r.batch_id))
            }
            results.append(result)
           
        yolo_records = AnnotationYoloClasses.get_all_records()
        
        # Get all batches that has no images
        for yolo in yolo_records:
            key2 = (yolo.client_id, yolo.factory_id, yolo.batch_id)
            if key2 not in existing_client_factory_batch_keys:
                existing_client_factory_batch_keys.add(key2)  # avoid duplicates

                client_name = Client.get_name_by_id(yolo.client_id)
                factory_name = Factory.get_name_by_id(yolo.factory_id)

                result = {
                    "client_id": yolo.client_id,
                    "client_name": client_name,
                    "factory_id": yolo.factory_id,
                    "factory_name": factory_name,
                    "batch_id": yolo.batch_id,
                    "annotated_images_count": 0,
                    "unannotated_images_count": 0,
                    "rejected_images_count": 0,
                    "disqualified_images_count": 0,
                    "background_images_count": 0,
                    "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(yolo.client_id, yolo.factory_id, yolo.batch_id))
                }
                results.append(result)

        all_factories = Factory.get_all_records()

        #Add summary of factories that has no images
        for factory in all_factories:
            key = (factory.client_id, factory.factory_id)
            if key not in existing_client_factory_keys:
                client_name = Client.get_name_by_id(factory.client_id)
                factory_name = Factory.get_name_by_id(factory.factory_id)

                result = {
                    "client_id": factory.client_id,
                    "client_name": client_name,
                    "factory_id": factory.factory_id,
                    "factory_name": factory_name,
                    "batch_id": 1,
                    "annotated_images_count": 0,
                    "unannotated_images_count": 0,
                    "rejected_images_count": 0,
                    "disqualified_images_count": 0,
                    "background_images_count": 0,
                    "Modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(factory.client_id, factory.factory_id, 1))
                }
                results.append(result)

        return results
    
    except Exception as e:
        print(f"Error in get_annotation_summary: {e}")
        return {"error": str(e)}
    
    
def get_dataset_summary(data):
    
    try:
        user_id = data["user_id"]

        datasets = AnnotationDataset.get_all_records()

        results = [
        {
            "dataset_id": record.dataset_id,
            "batch_id": record.batch_id,
            "name": record.name,
            "images": record.images,
            "dataset_url": record.dataset_url,
            "dataset_size": record.dataset_size,
            "client_id":record.client_id,
            "client_name": Client.get_name_by_id(record.client_id),
            "factory_id":record.factory_id,
            "factory_name": Factory.get_name_by_id(record.factory_id),
            "description": record.description,
            "created_at": record.created_at.strftime("%Y-%m-%d"),
            "updated_at": record.updated_at.strftime("%Y-%m-%d")
        } for record in datasets
        ] 
        
        return results

    except Exception as e:
        print(f"Error in get_dataset_summary: {e}")
        return {"error": str(e)}
        
        
def fetch_image_with_retry(url, retries=2, timeout=4, delay=1):
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                return response
            elif response.status_code == 404:
                print(f"[Abort] Image URL not found (404): {url}")
                return None
            else:
                print(f"[Retry {attempt+1}/{retries}] HTTP {response.status_code} error from: {url}")
        except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
            print(f"[Retry {attempt+1}/{retries}] Connection error: {e}")
        time.sleep(delay)
    print(f"[Error] Failed to fetch image after {retries} attempts: {url}")
    return None
    
    
def generate_dataset(data):
    try:
        success = True
        
        client_id = data["client_id"]
        client_name = Client.get_name_by_id(client_id) if Client.get_name_by_id(client_id) else None
        if client_name is None:
            success = False
            message = f"Client ID: {client_id} not exist in database."
            return success, message
        factory_id = data["factory_id"]
        factory_name = Factory.get_name_by_id(factory_id) if Factory.get_name_by_id(factory_id) else None
        if factory_name is None:
            success = False
            message = f"Factory ID: {factory_id} not exist in database."
            return success, message
            
        batch_id = data["batch_id"]
        
        image_records = db.session.query(
            AnnotationCameraImages
        ).select_from(AnnotationCameraImages
        ).filter(
            AnnotationCameraImages.client_id == client_id,
            AnnotationCameraImages.factory_id == factory_id,
            AnnotationCameraImages.batch_id == batch_id,
        ).filter(
            AnnotationCameraImages.dataset_id == 0, 
            AnnotationCameraImages.rejected == False,
            AnnotationCameraImages.disqualified == False,
            AnnotationCameraImages.annotation_status == True
        ).limit(4000).all()
        
        print("Total image_records:", len(image_records))
        
        total_number_of_image_records = len(image_records)
        if total_number_of_image_records > 0:
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dataset_folder = f"{client_name.lower()}_{factory_name.lower()}_{timestamp}"
            # Create the folder if it doesn't exist
            os.makedirs(dataset_folder, exist_ok=True)
            images_test_path = os.path.join(dataset_folder, 'test/images')
            images_train_path = os.path.join(dataset_folder, 'train/images')
            images_val_path = os.path.join(dataset_folder, 'val/images')
            labels_test_path = os.path.join(dataset_folder, 'test/labels')
            labels_train_path = os.path.join(dataset_folder, 'train/labels')
            labels_val_path = os.path.join(dataset_folder, 'val/labels')
            os.makedirs(images_test_path, exist_ok=True)
            os.makedirs(images_train_path, exist_ok=True)
            os.makedirs(images_val_path, exist_ok=True)
            os.makedirs(labels_test_path, exist_ok=True)
            os.makedirs(labels_train_path, exist_ok=True)
            os.makedirs(labels_val_path, exist_ok=True)
            
            train_image_count = int(round(0.85 * total_number_of_image_records))
            val_image_count = int(round(0.13 * total_number_of_image_records))
            
            #Create yolo index file
            yolo_record = AnnotationYoloClasses.get_record_by_client_factory_batch_id(client_id=client_id,factory_id=factory_id, batch_id=batch_id)
            if yolo_record is not None:
                names = Modules.get_records_by_list(yolo_record.modules)
                data = {
                    'names': names,
                    'nc': len(names),
                    'test': 'test/images',
                    'train': 'train/images',
                    'val': 'val/images'
                }
                yolo_index_file_path = os.path.join(dataset_folder, 'data.yaml')
                with open(yolo_index_file_path, 'w') as file:
                    yaml.dump(data, file, default_flow_style=False)

            
            images_dest_folder = images_train_path
            labels_dest_folder = labels_train_path
            counter = 1
            resolution_counts = defaultdict(int)
            background_images = 0
            counts = Counter()

            for record in image_records:
                
                if counter > train_image_count and counter < (train_image_count + val_image_count):
                    images_dest_folder = images_val_path
                    labels_dest_folder = labels_val_path
                if counter > (train_image_count + val_image_count): #Remaining will be test
                    images_dest_folder = images_test_path
                    labels_dest_folder = labels_test_path 


                if record.background_image == False:
                    #Get yolo data from database record
                    data_str = record.annotation
                    # print("annotation:", data_str)

                    if data_str == "{}":
                        print("No annotation found")
                        continue
                    
                    items = data_str.strip('{}').split('","')
                    yolo_data = [item.strip('"') for item in items]
                    
                    class_ids = [int(line.split()[0]) for line in yolo_data]
                    invalid_ids = [i for i in class_ids if i < 0 or i >= len(names)]
                    if invalid_ids:
                        print("Found invalid class IDs:", invalid_ids)
                        continue
                    
                    counts.update(class_ids) #Count the objects for dataset summary file
                
                else:
                    #Background images go to train only
                    images_dest_folder = images_train_path
                    labels_dest_folder = labels_train_path
                    background_images = background_images + 1
               

                #extract image name from record.image_url
                image_name = f"{record.camera_id}.{record.id}"
                
                #download image
                #print("image_url:", record.image_url)
                
                response = fetch_image_with_retry(record.image_url)

                if response:
                    image_file_name = f"{image_name}.jpg"
                    image_file_path = os.path.join(images_dest_folder, image_file_name)
                    with open(image_file_path, "wb") as f:
                        f.write(response.content)

                    # Read resolution
                    with pil_Image.open(image_file_path) as img:
                        width, height = img.size
                        resolution_key = f"{width}x{height}"
                        resolution_counts[resolution_key] += 1
                else:
                    print(f"Skipping image: {record.image_url}")
                    continue
                    
            
                
                # yolo_data = [
                    # "0 0.159545 0.252839 0.159091 0.255000",
                    # "1 0.554091 0.693672 0.288182 0.306667"
                # ]
                
                #create yolo annotation file
                yolo_file_name = f"{image_name}.txt"
                file_path = os.path.join(labels_dest_folder, yolo_file_name)

                if record.background_image == False:
                    with open(file_path, "w") as f:
                        for line in yolo_data:
                            f.write(line + "\n")
                else:
                    with open(file_path, "w") as f:
                        pass  #Creates an empty file
               
                counter = counter + 1
            

            annotated_images = total_number_of_image_records
            total_images = background_images + annotated_images
            
            # Build resolution distribution dictionary
            image_size_distribution = {}
            for resolution, count in resolution_counts.items():
                image_size_distribution[resolution] = count 
                
                
            # Print counts for each class
            # for i, name in enumerate(names):
                # print(f"{name}: {counts.get(i, 0)}")

            object_counts = {
                f"{names[class_id]} ({class_id})": count
                for class_id, count in counts.items()
            }


            # Write dataset summary file
            summary_data = {
                'Total images': total_images,
                'Background images (empty TXT)': background_images,
                'Annotated images': annotated_images,
                'Object counts': object_counts,
                'Image size distribution': image_size_distribution
            }
            
            dataset_summary_file_path = os.path.join(dataset_folder, 'dataset_summary.txt')
            with open(dataset_summary_file_path, 'w') as summary_file:
                yaml.dump(summary_data, summary_file, default_flow_style=False)
                    
            
            #Compress the folder
            zip_file = f"{dataset_folder}.zip"
            zip_folder(folder_path=dataset_folder, output_path=zip_file)
            
            # Get the size of the zip file in bytes
            zip_file_size = get_file_size(file_path=zip_file)
            
            #Upload the file in GCP storage
            destination_blob_name = f"yolo_dataset/{client_name.lower()}/{factory_name.lower()}/{zip_file}"
            upload_url = upload_blob(bucket_name="disrupt-hse-images",source_file_name=zip_file,destination_blob_name=destination_blob_name)
            
            #Delete folder and file
            delete_file(zip_file)
            delete_folder(dataset_folder)
            
            data = {
                "client_id": client_id,
                "factory_id": factory_id,
                "batch_id": batch_id,
                "name": zip_file,
                "images": total_number_of_image_records,
                "dataset_url": upload_url,
                "dataset_size": zip_file_size,
                "description": f"Yolo dataset"
            }
            
            dataset_record = AnnotationDataset.create(data)
            
            #Update AnnotationDataset ID in image records
            updated_data = [
                {
                    'id': record.id,
                    'dataset_id': dataset_record.dataset_id
                }
                for record in image_records
            ]
            AnnotationCameraImages.bulk_update(updated_data)
            
            success = True
            message = "Generating dataset! It will take time .."
        
        else:
            success = False
            message = f"Annotated images not available for the New Dataset."
                
        return success, message

    except Exception as e:
        print(f"Error in generate_dataset: {e}")
        return {"success": False, "message": str(e)}


def generate_dataset_using_websocket(data):
    """
    Generate dataset with real-time progress updates via WebSocket
    """
    try:
        success = True
        
        client_id = data["client_id"]
        factory_id = data["factory_id"]
        batch_id = data["batch_id"]
        
        # Create room identifier for this specific dataset generation
        room = f"dataset_{client_id}_{factory_id}_{batch_id}"
        
        def emit_progress(percentage, message, stage="processing"):
            """Helper function to emit progress updates"""
            try:
                socketio.emit('dataset_progress', {
                    'percentage': percentage,
                    'message': message,
                    'stage': stage,
                    'client_id': client_id,
                    'factory_id': factory_id,
                    'batch_id': batch_id
                }, room=room)
            except Exception as e:
                print(f"WebSocket emission error: {e}")
        
        # Emit initial progress
        emit_progress(0, "Starting dataset generation...", "initializing")
        
        # Validate client
        client_name = Client.get_name_by_id(client_id) if Client.get_name_by_id(client_id) else None
        if client_name is None:
            emit_progress(0, f"Client ID: {client_id} not exist in database.", "error")
            return False, f"Client ID: {client_id} not exist in database."
        
        emit_progress(5, "Client validation completed", "validating")
        
        # Validate factory
        factory_name = Factory.get_name_by_id(factory_id) if Factory.get_name_by_id(factory_id) else None
        if factory_name is None:
            emit_progress(0, f"Factory ID: {factory_id} not exist in database.", "error")
            return False, f"Factory ID: {factory_id} not exist in database."
        
        emit_progress(10, "Factory validation completed", "validating")
        
        # Get image records
        emit_progress(15, "Fetching image records...", "fetching")
        image_records = db.session.query(
            AnnotationCameraImages
        ).select_from(AnnotationCameraImages
        ).filter(
            AnnotationCameraImages.client_id == client_id,
            AnnotationCameraImages.factory_id == factory_id,
            AnnotationCameraImages.batch_id == batch_id,
        ).filter(
            AnnotationCameraImages.dataset_id == 0, 
            AnnotationCameraImages.rejected == False,
            AnnotationCameraImages.disqualified == False,
            AnnotationCameraImages.annotation_status == True
        ).limit(4000).all()
        
        total_number_of_image_records = len(image_records)
        emit_progress(20, f"Found {total_number_of_image_records} images to process", "fetching")
        
        if total_number_of_image_records > 0:
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dataset_folder = f"{client_name.lower()}_{factory_name.lower()}_{timestamp}"
            
            emit_progress(25, "Creating folder structure...", "setup")
            
            # Create folder structure
            os.makedirs(dataset_folder, exist_ok=True)
            images_test_path = os.path.join(dataset_folder, 'test/images')
            images_train_path = os.path.join(dataset_folder, 'train/images')
            images_val_path = os.path.join(dataset_folder, 'val/images')
            labels_test_path = os.path.join(dataset_folder, 'test/labels')
            labels_train_path = os.path.join(dataset_folder, 'train/labels')
            labels_val_path = os.path.join(dataset_folder, 'val/labels')
            
            # Create directories
            for path in [images_test_path, images_train_path, images_val_path, 
                        labels_test_path, labels_train_path, labels_val_path]:
                os.makedirs(path, exist_ok=True)
            
            train_image_count = int(round(0.85 * total_number_of_image_records))
            val_image_count = int(round(0.13 * total_number_of_image_records))
            
            emit_progress(30, "Creating YOLO configuration file...", "setup")
            
            # Create yolo index file
            yolo_record = AnnotationYoloClasses.get_record_by_client_factory_batch_id(
                client_id=client_id, factory_id=factory_id, batch_id=batch_id
            )
            
            if yolo_record is not None:
                # Create a dictionary for fast lookup to avoid sorting
                module_dict = {m.module_id: m.name for m in Modules.get_all_records()}
                names = [module_dict.get(module_id, "") for module_id in yolo_record.modules if module_id in module_dict]
                yolo_data = {
                    'names': names,
                    'nc': len(names),
                    'test': 'test/images',
                    'train': 'train/images',
                    'val': 'val/images'
                }
                yolo_index_file_path = os.path.join(dataset_folder, 'data.yaml')
                with open(yolo_index_file_path, 'w') as file:
                    yaml.dump(yolo_data, file, default_flow_style=False)

            emit_progress(35, "Starting image processing...", "processing")
            
            images_dest_folder = images_train_path
            labels_dest_folder = labels_train_path
            counter = 1
            resolution_counts = defaultdict(int)
            background_images = 0
            counts = Counter()

            for idx, record in enumerate(image_records):
                # Calculate progress (35% to 85% for image processing)
                progress = 35 + int((idx / total_number_of_image_records) * 50)
                
                if idx % 10 == 0:  # Emit progress every 10 images to avoid too many emissions
                    emit_progress(progress, f"Processing image {idx + 1} of {total_number_of_image_records}", "processing")
                
                # Determine destination folders based on split
                if counter > train_image_count and counter < (train_image_count + val_image_count):
                    images_dest_folder = images_val_path
                    labels_dest_folder = labels_val_path
                if counter > (train_image_count + val_image_count):
                    images_dest_folder = images_test_path
                    labels_dest_folder = labels_test_path 

                if record.background_image == False:
                    # Process annotation data
                    data_str = record.annotation
                    if data_str == "{}":
                        print("No annotation found")
                        continue
                    
                    items = data_str.strip('{}').split('","')
                    yolo_data = [item.strip('"') for item in items]
                    
                    class_ids = [int(line.split()[0]) for line in yolo_data]
                    invalid_ids = [i for i in class_ids if i < 0 or i >= len(names)]
                    if invalid_ids:
                        print("Found invalid class IDs:", invalid_ids)
                        continue
                    
                    counts.update(class_ids)
                else:
                    images_dest_folder = images_train_path
                    labels_dest_folder = labels_train_path
                    background_images += 1

                # Download and save image
                image_name = f"{record.camera_id}.{record.id}"
                response = fetch_image_with_retry(record.image_url)

                if response:
                    image_file_name = f"{image_name}.jpg"
                    image_file_path = os.path.join(images_dest_folder, image_file_name)
                    with open(image_file_path, "wb") as f:
                        f.write(response.content)

                    # Read resolution
                    with pil_Image.open(image_file_path) as img:
                        width, height = img.size
                        resolution_key = f"{width}x{height}"
                        resolution_counts[resolution_key] += 1
                else:
                    print(f"Skipping image: {record.image_url}")
                    continue
                
                # Create annotation file
                yolo_file_name = f"{image_name}.txt"
                file_path = os.path.join(labels_dest_folder, yolo_file_name)

                if record.background_image == False:
                    with open(file_path, "w") as f:
                        for line in yolo_data:
                            f.write(line + "\n")
                else:
                    with open(file_path, "w") as f:
                        pass  # Creates an empty file
               
                counter += 1

            emit_progress(85, "Creating dataset summary...", "finalizing")
            
            # Create dataset summary
            annotated_images = total_number_of_image_records
            total_images = background_images + annotated_images
            
            image_size_distribution = dict(resolution_counts)
            object_counts = {
                f"{names[class_id]} ({class_id})": count
                for class_id, count in counts.items()
            }

            summary_data = {
                'Total images': total_images,
                'Background images (empty TXT)': background_images,
                'Annotated images': annotated_images,
                'Object counts': object_counts,
                'Image size distribution': image_size_distribution
            }
            
            dataset_summary_file_path = os.path.join(dataset_folder, 'dataset_summary.txt')
            with open(dataset_summary_file_path, 'w') as summary_file:
                yaml.dump(summary_data, summary_file, default_flow_style=False)
            
            emit_progress(90, "Compressing dataset...", "compressing")
            
            # Compress the folder
            zip_file = f"{dataset_folder}.zip"
            zip_folder(folder_path=dataset_folder, output_path=zip_file)
            
            zip_file_size = get_file_size(file_path=zip_file)
            
            emit_progress(95, "Uploading to cloud storage...", "uploading")
            
            # Upload to cloud storage
            destination_blob_name = f"yolo_dataset/{client_name.lower()}/{factory_name.lower()}/{zip_file}"
            upload_url = upload_blob(
                bucket_name="disrupt-hse-images",
                source_file_name=zip_file,
                destination_blob_name=destination_blob_name
            )
            
            # Clean up local files
            delete_file(zip_file)
            delete_folder(dataset_folder)
            
            # Save to database
            dataset_data = {
                "client_id": client_id,
                "factory_id": factory_id,
                "batch_id": batch_id,
                "name": zip_file,
                "images": total_number_of_image_records,
                "dataset_url": upload_url,
                "dataset_size": zip_file_size,
                "description": f"Yolo dataset"
            }
            
            dataset_record = AnnotationDataset.create(dataset_data)
            
            # Update image records
            updated_data = [
                {
                    'id': record.id,
                    'dataset_id': dataset_record.dataset_id
                }
                for record in image_records
            ]
            AnnotationCameraImages.bulk_update(updated_data)
            
            emit_progress(100, "Dataset generation completed successfully!", "completed")
            
            success = True
            message = "Dataset generated successfully!"
        
        else:
            emit_progress(0, "No annotated images available for dataset generation.", "error")
            success = False
            message = "Annotated images not available for the New Dataset."
                
        return success, message

    except Exception as e:
        # Emit error progress
        room = f"dataset_{data.get('client_id')}_{data.get('factory_id')}_{data.get('batch_id')}"
        socketio.emit('dataset_progress', {
            'percentage': 0,
            'message': f"Error: {str(e)}",
            'stage': "error"
        }, room=room)
        
        print(f"Error in generate_dataset: {e}")
        return False, str(e)

def generate_dataset_async(data):
    """
    Wrapper function to run dataset generation in a separate thread
    """
    def run_generation():
        # Create application context for the background thread
        with app.app_context():
            try:
                success, message = generate_dataset_using_websocket(data)
                print(f"Dataset generation completed: {success}, {message}")
                return success, message
            except Exception as e:
                print(f"Error in background thread: {e}")
                # Emit error to WebSocket
                room = f"dataset_{data.get('client_id')}_{data.get('factory_id')}_{data.get('batch_id')}"
                socketio.emit('dataset_progress', {
                    'percentage': 0,
                    'message': f"Error: {str(e)}",
                    'stage': "error"
                }, room=room)
                return False, str(e)
    
    # Start the background thread
    thread = threading.Thread(target=run_generation)
    thread.daemon = True
    thread.start()
    
    return True, "Dataset generation started. Check progress via WebSocket."


def get_user_summary(data):
    try:
        user_id = data["user_id"]

        record = db.session.query(
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.rejected == True).label('rejected_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.disqualified == True).label('disqualified_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == True).label('annotated_images_count'),
            func.count(AnnotationCameraImages.id).filter(AnnotationCameraImages.annotation_status == False,AnnotationCameraImages.disqualified == False).label('unannotated_images_count')
        ).select_from(AnnotationCameraImages
        ).filter( 
            AnnotationCameraImages.annotation_user_id == user_id
        ).group_by(
            AnnotationCameraImages.annotation_user_id
        ).first() 
        
        user_summary = {
            "user_id": user_id,
            "rejected_images_count": record.rejected_images_count,
            "disqualified_images_count": record.disqualified_images_count,
            "annotated_images_count": record.annotated_images_count,
            "unannotated_images_count": record.unannotated_images_count
        }

        return user_summary
        
    except Exception as e:
        print(f"Error in get_user_summary: {e}")
        return {"error": str(e)}


def get_user_monthly_chart(data):
    try:
        user_id = data["user_id"]

        today = datetime.utcnow()
        year = today.year
        month = today.month
        days_in_month = calendar.monthrange(year, month)[1]

        # Subquery grouped by day
        daily_stats = db.session.query(
            extract('day', AnnotationCameraImages.updated_at).label('day'),
            func.count(case((AnnotationCameraImages.annotation_status == True, 1))).label('annotated_images_count'),
            #func.count(case((AnnotationCameraImages.rejected == True, 1))).label('rejected_images_count'),
            #func.count(case((AnnotationCameraImages.disqualified == True, 1))).label('disqualified_images_count'),
            #func.count(
            #    case(
            #        [(and_(
            #            AnnotationCameraImages.annotation_status == False,
            #            AnnotationCameraImages.disqualified == False
            #        ), 1)],
            #        else_=None
            #    )
            #).label('unannotated_images_count')
        ).filter(
            extract('year', AnnotationCameraImages.updated_at) == year,
            extract('month', AnnotationCameraImages.updated_at) == month,
            AnnotationCameraImages.annotation_user_id == user_id
        ).group_by(
            extract('day', AnnotationCameraImages.updated_at)
        ).order_by('day').all()

        # Initialize days with zeros
        days = list(range(1, days_in_month + 1))
        series = {
            "annotated_images_count": [0] * days_in_month,
            #"rejected_images_count": [0] * days_in_month,
            #"disqualified_images_count": [0] * days_in_month,
            #"unannotated_images_count": [0] * days_in_month,
        }

        # Fill values from query
        for row in daily_stats:
            day_index = int(row.day) - 1
            series["annotated_images_count"][day_index] = row.annotated_images_count
            #series["rejected_images_count"][day_index] = row.rejected_images_count
            #series["disqualified_images_count"][day_index] = row.disqualified_images_count
            #series["unannotated_images_count"][day_index] = row.unannotated_images_count

        chart_data = {
            "message": "User monthly chart fetched successfully.",
            "success": True,
            "data": {
                "series": [
                    {"name": key, "data": values}
                    for key, values in series.items()
                ],
                "categories": [str(day) for day in days]
            }
        }
        
        return chart_data
        
    except Exception as e:
        print(f"Error in get_user_summary_chart: {e}")
        return {"error": str(e)}
        
        
def get_all_users_summary():
    try:
        
        sql = text("""
        SELECT 
            aci.factory_id,
            aci.annotation_user_id,
            u.name,
            u.email,
            u.mobile_no,
            COUNT(*) FILTER (WHERE aci.rejected = TRUE) AS rejected_images_count,
            COUNT(*) FILTER (WHERE aci.disqualified = TRUE) AS disqualified_images_count,
            COUNT(*) FILTER (WHERE aci.annotation_status = TRUE) AS annotated_images_count,
            COUNT(*) FILTER (WHERE aci.annotation_status = FALSE) AS unannotated_images_count
        FROM ann_camera_images aci
        JOIN uam_user u ON u.user_id = aci.annotation_user_id
        WHERE aci.annotation_user_id != 0
        GROUP BY GROUPING SETS (
            (aci.factory_id, aci.annotation_user_id, u.user_id),
            (aci.annotation_user_id, u.user_id)
        )
        """)

        results = db.session.execute(sql).fetchall()
        
        user_summaries = []
        for row in results:
            user_summary = {
                "factory_id": row.factory_id,
                "factory_name": Factory.get_name_by_id(row.factory_id) if row.factory_id is not None else "ALL",
                "user_id": row.annotation_user_id,
                "name": row.name,
                "email": row.email,
                "mobile_no": row.mobile_no,
                "rejected_images_count": row.rejected_images_count,
                "disqualified_images_count": row.disqualified_images_count,
                "annotated_images_count": row.annotated_images_count,
                "unannotated_images_count": row.unannotated_images_count,
            }
            user_summaries.append(user_summary)

        # print(user_summaries)

        return {"users_summary": user_summaries}

    except Exception as e:
        print(f"Error in get_all_users_summary: {e}")
        return {"error": str(e)}


def get_all_images(data):
    try:
        annotation_user_id = data.get("annotation_user_id")
        factory_id = data.get("factory_id")
        status = data.get("status", "")
        page = int(data.get("page", 1))
        per_page = int(data.get("per_page", 20))
        

        # Mandatory filters
        if not annotation_user_id or not factory_id:
            return {"error": "annotation_user_id and factory_id are required."}


        query = db.session.query(AnnotationCameraImages).filter(
            AnnotationCameraImages.annotation_user_id == annotation_user_id,
            AnnotationCameraImages.factory_id == factory_id
        )      


        # Status filters
        if status == "annotated":
            query = query.filter(AnnotationCameraImages.annotation_status == True)
        elif status == "rejected":
            query = query.filter(AnnotationCameraImages.rejected == True)
        elif status == "disqualified":
            query = query.filter(AnnotationCameraImages.disqualified == True)
        elif status == "background":
            query = query.filter(AnnotationCameraImages.background_image == True)    


        # Order and pagination
        query = query.order_by(AnnotationCameraImages.created_at.desc())
        images = query.offset((page - 1) * per_page).limit(per_page).all()

        results = []
        for record in images:
            results.append({
                "id": record.id,
                "camera_id": record.camera_id,
                "image_url": record.image_url,
                "annotation_user_id": record.annotation_user_id,
                "annotation_user_name": Users.get_name_by_user_id(record.annotation_user_id),
                "review_user_id": record.review_user_id,
                "review_user_name": Users.get_name_by_user_id(record.review_user_id),
                "annotation": record.annotation,
                "annotation_status": getattr(record, "annotation_status", None),
                "rejected": getattr(record, "rejected", None),
                "review_status": getattr(record, "review_status", None),
                "review_comments": getattr(record, "review_comments", None),
                "disqualified": getattr(record, "disqualified", None),
                "detection_model": getattr(record, "detection_model", None),
                "background_image": getattr(record, "background_image", None),
                "modules": Modules.get_records_by_list(
                    AnnotationYoloClasses.get_modules(record.client_id, record.factory_id, record.batch_id)
                ),
                "created_at": record.created_at.strftime("%Y-%m-%d") if record.created_at else None,
                "updated_at": record.updated_at.strftime("%Y-%m-%d") if record.updated_at else None
            })
        return results
    except Exception as e:
        print(f"Error in get_all_images: {e}")
        return {"error": str(e)}



def get_user_job_progress():
    try:
        results = db.session.query(
            AnnotationCameraImages.job_id.label("job_id"),
            AnnotationCameraImages.annotation_user_id.label("annotation_user_id"),
            AnnotationCameraImages.client_id.label("client_id"),
            AnnotationCameraImages.factory_id.label("factory_id"),
            AnnotationCameraImages.batch_id.label("batch_id"),
            AnnotationJobAssignment.limit.label("limit"),
            AnnotationJobAssignment.status.label("status"),
            func.count().label("total_images"),
            func.sum(
                case(
                    (AnnotationCameraImages.annotation_status == True, 1),
                    else_=0
                )
            ).label("annotated_images_count"),
            func.sum(
                case(
                    ((AnnotationCameraImages.annotation_status == False) & (AnnotationCameraImages.disqualified == False), 1),
                    else_=0
                )
            ).label("unannotated_images_count"),
            func.sum(
                case(
                    (AnnotationCameraImages.disqualified == True, 1),
                    else_=0
                )
            ).label("disqualified_images_count"),
            func.sum(
                case(
                    (AnnotationCameraImages.rejected == True, 1),
                    else_=0
                )
            ).label("rejected_images_count"),
            func.sum(
                case(
                    (AnnotationCameraImages.review_status == True, 1),
                    else_=0
                )
            ).label("reviewed_images_count")
        ).join(
            AnnotationJobAssignment, AnnotationCameraImages.job_id == AnnotationJobAssignment.job_id
        ).filter(
            AnnotationCameraImages.job_id != None
        ).group_by(
            AnnotationCameraImages.job_id,
            AnnotationCameraImages.annotation_user_id,
            AnnotationCameraImages.client_id,
            AnnotationCameraImages.factory_id,
            AnnotationCameraImages.batch_id,
            AnnotationJobAssignment.limit,
            AnnotationJobAssignment.status
        ).all()

        
        user_summaries = []
        for row in results:

            # Get rejected images count from AnnotationUserDetails table
            user_detail = AnnotationUserDetails.get_by_user_and_job(row.annotation_user_id, row.job_id)
            rejected_images_count_from_user_details = user_detail.rejected_images_count if user_detail else 0

            user_summary = {
                "job_id": row.job_id,
                "annotation_user_id": row.annotation_user_id,
                "annotation_user_name": Users.get_name_by_user_id(row.annotation_user_id),
                "client_id": row.client_id,
                "client_name": Client.get_name_by_id(row.client_id),
                "factory_id": row.factory_id,
                "factory_name": Factory.get_name_by_id(row.factory_id),
                "batch_id": row.batch_id,
                "modules": Modules.get_records_by_list(AnnotationYoloClasses.get_modules(row.client_id, row.factory_id, row.batch_id)),
                "status": row.status,
                "total_images": row.total_images,
                "annotated_images_count": row.annotated_images_count,
                "unannotated_images_count": row.unannotated_images_count,
                "rejected_images_count": rejected_images_count_from_user_details,
                "disqualified_images_count": row.disqualified_images_count,
                "reviewed_images_count": row.reviewed_images_count
            }
            user_summaries.append(user_summary)

        # print(user_summaries)

        return {"users_summary": user_summaries}

    except Exception as e:
        print(f"Error in get_all_users_summary: {e}")
        return {"error": str(e)}


def transfer_annotation_job(data):
    try:
        job_id = data["job_id"]
        user_id = data["user_id"]

        # Get the existing job
        job = AnnotationJobAssignment.get_record_by_id(job_id)
        if not job:
            return {"message": "Job does not exist"}, 400
        
        # Get remaining unannotated images from the current job
        unannotated_images = db.session.query(AnnotationCameraImages).filter(
            and_(
                AnnotationCameraImages.job_id == job_id,
                AnnotationCameraImages.annotation_status == False,
                AnnotationCameraImages.disqualified == False,
                AnnotationCameraImages.background_image == False
            )
        ).all()
        
        if not unannotated_images:
            return {"message": "No unannotated images available to transfer"}, 400
        
        
        try:
            # Create a new job with inherited data from the previous job
            new_job = AnnotationJobAssignment(
                user_id=user_id,
                client_id=job.client_id,
                factory_id=job.factory_id,
                batch_id=job.batch_id,
                limit=len(unannotated_images),
                status="pending",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.session.add(new_job)
            db.session.flush()  # This gets the new job_id without committing
            

            # Update the unannotated images with new job_id and user_id
            update_images = [
                {"id": img.id, "job_id": new_job.job_id, "annotation_user_id": user_id}
                for img in unannotated_images
            ]
            
            # Bulk update images - if this fails, everything will rollback
            update_result = AnnotationCameraImages.bulk_update(update_images)
            if not update_result["success"]:
                raise Exception(f"Failed to update images: {update_result['message']}")
            
            
            # Update the original job's limit and status to reflect the transferred images
            job.limit = job.limit - len(unannotated_images)
            job.status = "completed" 
            job.updated_at = datetime.utcnow()
            
            # Commit all changes together
            db.session.commit()
            
            return {
                "message": "Job transferred successfully.",
                "original_job_id": job_id,
                "new_job_id": new_job.job_id,
                "transferred_images_count": len(unannotated_images)
            }, 200
            
        except Exception as inner_e:
            # Rollback all changes if any operation fails
            db.session.rollback()
            print(f"Transaction failed, rolling back: {inner_e}")
            raise inner_e
        
    except Exception as e:
        # Ensure rollback in case of any outer exception
        db.session.rollback()
        print(f"Error in transfer_annotation_job: {e}")
        return {"message": f"Error: {e}"}, 400


def get_all_annotation_jobs(data):
    try:
        user_id = data.get("user_id", None)
        filters = data.get("filters", {})
        factory_id = filters.get("factory_id", 0)
        client_id = filters.get("client_id", 0)
        batch_id = filters.get("batch_id", 0)
        status = filters.get("status", "").lower()
        limit = filters.get("limit", 0)

        start_date, end_date = parse_filters(data)

        query = db.session.query(AnnotationJobAssignment).filter(
            AnnotationJobAssignment.created_at >= start_date,
            AnnotationJobAssignment.created_at <= end_date
        ).order_by(AnnotationJobAssignment.created_at.desc())
        
        if user_id: 
            query = query.filter(AnnotationJobAssignment.user_id == user_id)
        if factory_id:
            query = query.filter(AnnotationJobAssignment.factory_id == factory_id)
        if client_id:
            query = query.filter(AnnotationJobAssignment.client_id == client_id)
        if batch_id:
            query = query.filter(AnnotationJobAssignment.batch_id == batch_id)
        if status:
            query = query.filter(AnnotationJobAssignment.status == status)
        if limit:
            query = query.filter(AnnotationJobAssignment.limit == limit)

        records = query.all()

        result = []
        for rec in records:
            yolo_class = AnnotationYoloClasses.get_record_by_client_factory_batch_id(
                client_id=rec.client_id,
                factory_id=rec.factory_id,
                batch_id=rec.batch_id
            )

            annotated_images_count = AnnotationCameraImages.query.filter(
                AnnotationCameraImages.job_id == rec.job_id,
                AnnotationCameraImages.annotation_status == True
            ).count()

            reviewed_images_count = AnnotationCameraImages.query.filter(
                AnnotationCameraImages.job_id == rec.job_id,
                AnnotationCameraImages.review_status == True
            ).count()

            result.append({
                "job_id": rec.job_id,
                "user_id": rec.user_id,
                "user_name": Users.get_name_by_user_id(rec.user_id),
                "client_id": rec.client_id,
                "client_name": Client.get_name_by_id(rec.client_id),
                "factory_id": rec.factory_id,
                "factory_name": Factory.get_name_by_id(rec.factory_id),
                "batch_id": rec.batch_id,
                "modules": Modules.get_id_name_by_list(yolo_class.modules) if yolo_class and yolo_class.modules else [],
                "annotated_images_count": annotated_images_count,
                "reviewed_images_count": reviewed_images_count,
                "limit": rec.limit,
                "status": rec.status,
                "created_at": rec.created_at.strftime("%Y-%m-%d"),
                "updated_at": rec.updated_at.strftime("%Y-%m-%d")
            })

        return result

    except Exception as e:
        print(f"Error in get_all_annotation_jobs: {e}")
        return {"error": str(e)}

    
def get_training_image_counts_per_camera(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")

        query = db.session.query(
            AnnotationCameraImages.camera_id.label("camera_id"),
            func.count(AnnotationCameraImages.id).label("image_count")
        ).filter(
            AnnotationCameraImages.dataset_id != 0,
            AnnotationCameraImages.client_id == client_id,
            AnnotationCameraImages.factory_id == factory_id
        ).group_by(AnnotationCameraImages.camera_id).all()

        output = []
        for camera_id, count in query:
            camera_name = Cameras.get_name_by_camera_id(camera_id)
            output.append({
                "camera_id": camera_id,
                "camera_name": camera_name[0] if camera_name else "",
                "image_count": count
            })
        return output

    except Exception as e:
        print(f"Error in get_training_image_counts_per_camera: {e}")
        return {"error": str(e)}


def insert_false_alert_images(data):
    req_fields = ["images"]
    for field in req_fields:
        if not data.get(field):
            return {"message": f"{field} is missing."}, 400

    rows = data.get("images")
    images_to_insert = []
    errors = []

    for idx, img in enumerate(rows):
        missing = [f for f in ["client_id", "factory_id", "module"] if not img.get(f)]
        if missing:
            errors.append(f"{', '.join(missing)} is missing in image at index {idx}")
            continue

        client_id = img["client_id"]
        factory_id = img["factory_id"]
        module = img["module"].lower()

        yolo_batches = AnnotationYoloClasses.query.filter_by(
            client_id=client_id,
            factory_id=factory_id
        ).all()

        batch_id = None
        for yolo in yolo_batches:
            module_ids = yolo.modules if isinstance(yolo.modules, list) else []
            module_names = [m.lower() for m in Modules.get_records_by_list(module_ids)]
            if module in module_names:
                batch_id = yolo.batch_id
                break

        if not batch_id:
            errors.append(f"No batch found for client_id={client_id}, factory_id={factory_id}, module={module} at index {idx}")
            continue

        img["batch_id"] = batch_id
        images_to_insert.append(img)

    any_rows_added = False
    if images_to_insert:
        any_rows_added = AnnotationCameraImages.add_multiple_rows_skip_existing(images_to_insert)

    response = {}
    if any_rows_added:
        if errors:
            response["message"] = "Images inserted successfully, but some records were skipped due to unmatched modules."
        else:
            response["message"] = "Images inserted successfully."
    else:
        response["message"] = "No rows were added."

    if errors:
        response["errors"] = errors

    if any_rows_added:
        return response, 200
    else:
        return response, 400