from api import hse_ns

from flask_restx import fields


# Add Module Payload
add_module_payload = hse_ns.model(
    "AddOrUpdateModule",
    {
        "module_id": fields.Integer(
            required=False, description="Module ID (for update only)"
        ),
        "name": fields.String(
            default="Helmet", required=True, description="The AI Module Name"
        ),
    },
)

insert_alerts_payload = hse_ns.model(
    "Insert Alerts",
    {
        "alerts": fields.List(
            fields.Nested(
                hse_ns.model(
                    "Alert",
                    {
                        "alert_id": fields.Integer(
                            required=True,
                            description="ID of the alert",
                        ),
                        "factory_id": fields.Integer(
                            required=True, description="Factory ID"
                        ),
                        "client_id": fields.Integer(
                            required=True, description="Client ID"
                        ),
                        "module_id": fields.Integer(
                            required=True, description="Module ID"
                        ),
                        "compliant": fields.Boolean(
                            required=True, description="Compliant"
                        ),
                        "unannotated_url": fields.String(
                            required=True, description="Unannotated URL"
                        ),
                        "annotated_url": fields.String(
                            required=True, description="Annotated URL"
                        ),
                        "camera_id": fields.String(
                            required=True, description="Camera ID"
                        ),
                        "zone_id": fields.Integer(
                            required=True, description="Zone ID", default=0
                        ),
                        "timestamp": fields.DateTime(
                            required=True, description="Timestamp of alert"
                        ),
                    },
                )
            )
        )
    },
)
 

hse_alerts_zone_wise_payload = hse_ns.model("FilterPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "filters": fields.Nested(
        hse_ns.model("Filters", {
            "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
            "week": fields.String(required=False, description="Filter by week number (e.g. 2025-W32)"),
            "month": fields.String(required=False, description="Filter by month (e.g. 2025-08)"),
            "starting": fields.String(required=False),
            "ending": fields.String(required=False),
            
        }),
        required=True,
        description="Filter options"
    )
})

zone_status_payload = hse_ns.model("ZoneStatusPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "filters": fields.Nested(
        hse_ns.model("ZoneFilters", {
            "date": fields.String(required=False),
            "week": fields.String(required=False),
            "month": fields.String(required=False),
            "starting": fields.String(required=False),
            "ending": fields.String(required=False),
        }),
        required=True
    )
})

alert_trend_payload = hse_ns.model("AlertTrendPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "filters": fields.Nested(
        hse_ns.model("TrendFilters", {
            "date": fields.String(required=False),
            "week": fields.String(required=False),
            "month": fields.String(required=False),
            "starting": fields.String(required=False),
            "ending": fields.String(required=False),
        }),
        required=True
    )
})

hse_summary_payload = hse_ns.model("HseSummaryPayload", {
    "client_id": fields.Integer(required=True),
    "factory_id": fields.Integer(required=True)
})


live_alerts_payload = hse_ns.model("LiveAlertsPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "module_ids": fields.List(fields.Integer, required=False, description="Filter by module IDs"),
    "zone_ids": fields.List(fields.Integer, required=False, description="Filter by zone IDs"),
    "area_ids": fields.List(fields.Integer, required=False, description="Filter by area IDs"),
    "sub_area_ids": fields.List(fields.Integer, required=False, description="Filter by sub area IDs"),
    "filters": fields.Nested(
        hse_ns.model("LiveAlertFilters", {
            "date": fields.String(required=False),
            "week": fields.String(required=False),
            "month": fields.String(required=False),
            "starting": fields.String(required=False),
            "ending": fields.String(required=False),
        }),
        required=True
    ),
    "pagination": fields.Nested(hse_ns.model('AlertsPagination', {
        "page_no": fields.Integer(default=1, description="Page No."),
        "per_page": fields.Integer(default=21, description="Records per page"),
    }), description="Pagination for Live Alerts"),
})

#add factory modules payloads
add_factory_module_payloads = hse_ns.model(
    "AddOrUpdateFactroyModuels",
    {
        "factory_id": fields.Integer(
            required=False, description="Module ID (for update only)"
        ),
       "module_ids": fields.List(
           fields.Integer, required=False, description="Filter by module IDs")
    }
)

dynamic_alert_trend_analytics_payload = hse_ns.model("AlertTrendAnalyticsPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "x_axis": fields.String(required=False, default="modules", description="X-axis dimension: modules, zones, areas, subareas"),
    "module_ids": fields.List(fields.Integer, required=False, description="Filter by module IDs"),
    "zone_ids": fields.List(fields.Integer, required=False, description="Filter by zone IDs"),
    "area_ids": fields.List(fields.Integer, required=False, description="Filter by area IDs"),
    "sub_area_ids": fields.List(fields.Integer, required=False, description="Filter by sub area IDs"),
    "filters": fields.Nested(
        hse_ns.model("TrendAnalyticsFilters", {
            "date": fields.String(required=False, description="Specific date (YYYY-MM-DD)"),
            "week": fields.String(required=False, description="Week filter"),
            "month": fields.String(required=False, description="Month filter (YYYY-MM)"),
            "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
            "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
        }),
        required=False
    ),
})

overall_compliance_score_payload = hse_ns.model("OverallComplianceScorePayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "breakdown": fields.String(required=False, description="Breakdown type (zone/area/subarea)", default="zone"),
    "filters": fields.Nested(
        hse_ns.model("OverallComplianceFilters", {
            "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
            "week": fields.String(required=False, description="Filter by week number (e.g. 2025-W32)"),
            "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
            "start_date": fields.String(required=False, description="Custom start date (YYYY-MM-DD)"),
            "end_date": fields.String(required=False, description="Custom end date (YYYY-MM-DD)")
        })
    )
})


dynamic_heatmap_payload = hse_ns.model("DynamicHeatmapPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "x_axis": fields.String(
        required=True, 
        description="X-axis dimension",
        enum=["modules", "zones", "areas", "sub_areas"]
    ),
    "y_axis": fields.String(
        required=True, 
        description="Y-axis dimension", 
        enum=["modules", "zones", "areas", "sub_areas"]
    ),
    "filters": fields.Nested(
        hse_ns.model("HeatmapFilters", {
            "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
            "week": fields.String(required=False, description="Filter by week (YYYY-WXX)"),
            "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
            "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
            "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
        }),
        required=True,
        description="Date filters"
    )
})


get_months_accepted_records_payload = hse_ns.model("GetMonthsAcceptedRecordsPayload", {
    "client_id": fields.Integer(required=True, description="Client ID"),
    "factory_id": fields.Integer(required=True, description="Factory ID"),
    "filters": fields.Nested(
        hse_ns.model("MonthsAcceptedRecordsFilters", {
            "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
            "week": fields.String(required=False, description="Filter by week (YYYY-WXX)"),
            "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
        }),
        required=True,
        description="Date filters"
    )
})

compliance_summary_payload = hse_ns.model(
    "Get Compliance Summary Payload",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "filters": fields.Nested(
            hse_ns.model("ComplianceSummaryFilters", {
                "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
                "week": fields.String(required=False, description="Filter by week (YYYY-WXX)"),
                "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
                "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                "area_ids": fields.List(fields.Integer, required=False, description="Filter by area IDs"),
            }),
            description="Date filters"
        )
    },
)

compliance_accuracy_fetch_payload = hse_ns.model(
    "Fetch Compliance Accuracy",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "filters": fields.Nested(
            hse_ns.model("ComplianceAccuracyFilters", {
                "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
                "week": fields.String(required=False, description="Filter by week (YYYY-WXX)"),
                "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
                "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                "area_ids": fields.List(fields.Integer, required=False, description="Filter by area IDs"),
                "shift": fields.List(fields.String, required=False, description="Filter by shift"),
            }),
            description="Filters"
        )
    },
)


compliance_by_camera_summary_payload = hse_ns.model(
    "Fetch Compliance By Camera Summary",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "filters": fields.Nested(
            hse_ns.model("ComplianceByCameraSummaryFilters", {
                "date": fields.String(required=False, description="Filter by specific date (YYYY-MM-DD)"),
                "week": fields.String(required=False, description="Filter by week (YYYY-WXX)"),
                "month": fields.String(required=False, description="Filter by month (YYYY-MM)"),
                "starting": fields.String(required=False, description="Start date (YYYY-MM-DD)"),
                "ending": fields.String(required=False, description="End date (YYYY-MM-DD)"),
                "area_ids": fields.List(fields.Integer, required=False, description="Filter by area IDs"),
                "shift": fields.List(fields.String, required=False, description="Filter by shift"),
            }),
            description="Filters"
        )
    },
)