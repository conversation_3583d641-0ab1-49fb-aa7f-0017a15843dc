from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import desc, func

class Modules(db.Model):
    __tablename__ = 'hse_modules'
    module_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.Text, nullable=False)
    color = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    active = db.Column(db.<PERSON>, default=True, nullable=False)

    @classmethod
    def get_by_id(cls, module_id):
        return cls.query.filter_by(module_id=module_id, active=True).first()
    
    @classmethod
    def get_all_records(cls):
        return cls.query.filter_by(active=True).all()
    
    @classmethod
    def get_ids_by_list(cls,id_list):
        final_list=[]
        find_record=cls.query.filter(cls.module_id.in_(id_list)).all()
        if find_record:
            final_list= [x.module_id for x in find_record if x.active]
        return final_list
    
    @classmethod
    def get_module_name(cls, moduleID):
        return cls.query.with_entities(cls.name).filter_by(module_id=moduleID).first()


    @classmethod
    def get_records_by_list(cls,ids_list):
        get_list = cls.query.filter(cls.module_id.in_(ids_list)).all()
        
        # Create a dictionary for quick lookup
        module_dict = {item.module_id: item.name for item in get_list if item.active}
        
        # Return in the same order as ids_list, preserving the original sequence
        response = []
        for module_id in ids_list:
            if module_id in module_dict:
                response.append(module_dict[module_id])
        
        return response
        
        
    @classmethod
    def get_id_name_by_list(cls, ids_list):
        get_list = cls.query.filter(cls.module_id.in_(ids_list)).all()
        
        # Create a dictionary for quick lookup
        module_dict = {item.module_id: {"module_id": item.module_id, "name": item.name} for item in get_list if item.active}
        
        # Return in the same order as ids_list, preserving the original sequence
        response = []
        for module_id in ids_list:
            if module_id in module_dict:
                response.append(module_dict[module_id])
        
        return response
    
    @classmethod
    def createUpdate(cls, data):
        """
        If 'module_id' is provided in data, update the existing module.
        Otherwise, create a new module.
        Returns a tuple: (module_instance, action) where action is 'added' or 'updated'.
        """
        module_id = data.get("module_id")
        if module_id:
            module = cls.query.filter_by(module_id=module_id, active=True).first()
            if module:
                name = data.get("name")
                if name:
                    module.name = name
                module.updated_at = datetime.datetime.utcnow()
                db.session.commit()
                return module, "updated"
        # Create new
        new_rec = cls(
            name=data["name"],
            active=True,
            created_at=datetime.datetime.utcnow()
        )
        db.session.add(new_rec)
        db.session.commit()
        return new_rec, "added"
    
    @classmethod
    def create_multiple(cls, data):
            existing_modules = cls.query.all()
            existing_module_names = [module.name for module in existing_modules]
            print("Existing: ",existing_module_names)
            new_recs = [cls(name=class_name.strip(), active=True, created_at=datetime.datetime.utcnow()) for class_name in data["modules"] if class_name.strip() and class_name.strip() not in existing_module_names]
            db.session.add_all(new_recs)
            db.session.commit()
            return [rec.module_id for rec in new_recs]

    def delete(self):
            db.session.delete(self)
            db.session.commit()
            
    @classmethod
    def toggle_status(cls, id):
        rec = cls.query.filter_by(module_id=id).first()
        if rec:
            rec.active = not rec.active
            db.session.commit()
            return rec
        return None
    
class Severity(db.Model):
    __tablename__ = 'hse_severity'
    severity_id = db.Column(db.Integer, primary_key=True)
    severity = db.Column(db.Text, nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('hse_modules.module_id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    @classmethod
    def get_name_by_id(cls, severity_id):
        rec = cls.query.filter_by(severity_id=severity_id).first()
        return rec.severity
    
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
    


class Alerts(db.Model):
    __tablename__ = 'hse_alerts'
    __table_args__ = (
        db.Index('idx_alerts_client_factory', 'client_id', 'factory_id'),
        db.Index('idx_alerts_module_id', 'module_id'),
        db.Index('idx_alerts_camera_id', 'camera_id'),
    )
    
    alert_id = db.Column(db.BigInteger, primary_key=True)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('hse_modules.module_id'), nullable=False)
    zone_id = db.Column(db.Integer, nullable=True)
    camera_id = db.Column(db.Text, nullable=False)
    compliant = db.Column(db.Boolean, nullable=False, default=False)
    unannotated_url = db.Column(db.Text, nullable=False, default="")
    annotated_url = db.Column(db.Text, nullable=True, default="")
    techqa = db.Column(db.Boolean, nullable=True)
    aitechqa = db.Column(db.Boolean, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    local_db_id = db.Column(db.Integer, nullable=False)
    
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_id)).first()
        return latest_row.local_db_id if latest_row else None
        
    @classmethod
    def sync_alerts(cls, data):
        new_alerts = []
        updated_count = 0
        
        for alert in data:
            # Check if alert already exists based on local_db_id
            existing_alert = cls.query.filter_by(
                local_db_id=alert["alert_id"],
                factory_id=alert["factory_id"],
                client_id=alert["client_id"]
            ).first()
            
            if existing_alert:
                # Update existing alert
                existing_alert.module_id = alert["module_id"]
                existing_alert.compliant = alert["compliant"]
                existing_alert.unannotated_url = alert["unannotated_url"]
                existing_alert.annotated_url = alert["annotated_url"]
                existing_alert.camera_id = alert["camera_id"]
                existing_alert.zone_id = alert.get("zone_id", 0)
                existing_alert.updated_at = datetime.datetime.utcnow()
                updated_count += 1
            else:
                # Create new alert
                new_alert = cls(
                    local_db_id=alert["alert_id"],
                    factory_id=alert["factory_id"],
                    client_id=alert["client_id"],
                    module_id=alert["module_id"],
                    compliant=alert["compliant"],
                    unannotated_url=alert["unannotated_url"],
                    annotated_url=alert["annotated_url"],
                    camera_id=alert["camera_id"],
                    zone_id=alert.get("zone_id", 0),
                    timestamp=datetime.datetime.strptime(alert["timestamp"], '%Y-%m-%dT%H:%M:%S.%fZ'),
                    updated_at=datetime.datetime.utcnow()
                )
                new_alerts.append(new_alert)
        
        if new_alerts or updated_count > 0:
            if new_alerts:
                db.session.add_all(new_alerts)
            db.session.commit()
            return True
        return False
    

    @classmethod
    def get_by_id(cls, alert_id):
        return cls.query.filter_by(alert_id=alert_id).first()
    
    @classmethod
    def get_all_records(cls, factory_id=None, client_id=None, module_id=None):
        return cls.query.filter_by(factory_id=factory_id, client_id=client_id, module_id=module_id).all()

    
class Factory_Modules(db.Model):
    __tablename__ = 'hse_factory_modules'

    id = db.Column(db.Integer, primary_key=True)
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('hse_modules.module_id'), nullable=False)

    @classmethod
    def createUpdate(cls, factory_id, module_id, **kwargs):
        """
        Create or update a factory-module association.
        
        Args:
            factory_id (int): The ID of the factory
            module_id (int): The ID of the module
            **kwargs: Additional attributes to update if record exists
            
        Returns:
            tuple: (factory_module_instance, created) where created is boolean
        """
        # Try to find existing record
        factory_module = cls.query.filter_by(
            factory_id=factory_id,
            module_id=module_id
        ).first()

        created = False
        
        if factory_module:
            # Update existing record
            for key, value in kwargs.items():
                if hasattr(factory_module, key):
                    setattr(factory_module, key, value)
        else:
            # Create new record
            factory_module = cls(
                factory_id=factory_id,
                module_id=module_id,
                **kwargs
            )
            db.session.add(factory_module)
            created = True
        
        try:
            db.session.commit()
            return factory_module, created
        except Exception as e:
            db.session.rollback()
            raise e

    @classmethod
    def get_modules_by_factory_id(cls, factory_id):
        results = db.session.query(
            cls.module_id,
            Modules.name
        ).join(
            Modules, cls.module_id == Modules.module_id
        ).filter(
            cls.factory_id == factory_id,
            Modules.active == True 
        ).all()
        
        return [
            {
                "module_id": result.module_id,
                "name": result.name
            }
            for result in results
        ]

        