from flask_restx import Resource, abort
from api.models.assetModels import *
from api.models.facialModels import *
from api.models.uamModels import *

from api.payloads.facialPayloads import *
from api import facial_ns
from api.controllers import facialController as facial_func

from api import db, mqtt_client
from api.services import token_required, user_token_required
from sqlalchemy import and_, or_
import json
import re

@facial_ns.route("/sync_rows")
@facial_ns.doc("Sync Rows")
class FacialSyncRows(Resource):
    @facial_ns.doc("Sync Rows")
    @facial_ns.expect(facial_sync_payload_model)
    @facial_ns.response(200, "Rows Synced successfully.")
    @facial_ns.response(400, "Validation Error")
    def post(self):
        data = facial_ns.payload
        try:
            models=[FacialRecognitionLogs, FacialEmployeeEmbeddings, FacialEmployees] 
            for field in models:
                if field.__tablename__ in data.keys():
                    rows = data[field.__tablename__]
                    any_rows_added = field.add_multiple_rows(rows) 
                    
             # Generate the appropriate message
            if any_rows_added:
                message = "Rows Synced successfully."
            else:
                message = "No rows were added."
            
            response = {"message": message}  
                
            return response, 200 

        except Exception as e:
            db.session.rollback()  # Rollback any failed transactions
            print(str(e))
            abort(400, {
                "success": False,
                "message": "Failed to sync rows",
                "error": str(e)
            })
            
            
@facial_ns.route("/get_db_id/<string:client_id>/<string:factory_id>/<string:table_name>")
@facial_ns.doc("Get DB ID")
class FacialGetDBID(Resource):
    # @token_required
    @facial_ns.doc("Get DB ID")
    @facial_ns.response(200, "DB ID fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    # @sub_area_ns.doc(security=["Authorization", "Token-Type"])
    def get(self,client_id, factory_id, table_name):
        try:
            if client_id and factory_id and table_name: 
                models=[FacialRecognitionLogs, FacialEmployeeEmbeddings, FacialEmployees] 
                get_table= [x for x in models if x.__tablename__.lower() == table_name.lower()]
                print("got table: ", get_table)
                if get_table and get_table[0]:
                    
                    resp = get_table[0].get_local_db_id(client_id=client_id, factory_id=factory_id) 
                    table= get_table[0].__tablename__
                    print("Resp: ", resp)
                    
                    return {
                        "message": "DB ID fetched successfully" if resp is not None else "ID does not exist",
                        "success":  True  if resp is not None else False,
                        "id": resp  if resp is not None else 0, 
                        "table": table
                        }, 200
                else:
                    return {
                    "message": "Table not found" ,
                    "success":  False,
                    }, 400
            else:
                return {
                "message": "Insufficient data" ,
                "success":  False,
                }, 400
                
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@facial_ns.route("/get_live_logs")
@facial_ns.doc("Get Live Facial Logs")
class FacialGetLiveLogs(Resource):
    @facial_ns.doc("Get Live Facial Logs")
    @facial_ns.expect(facial_live_logs_payload)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            logs = facial_func.get_live_logs(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": logs
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@facial_ns.route("/get_live_logss")
@facial_ns.doc("Get Live Facial Logs")
class FacialGetLiveLogss(Resource):
    @facial_ns.doc("Get Live Facial Logs")
    @facial_ns.expect(facial_live_logs_payload)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            logs = facial_func.get_live_logss(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": logs
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            

@facial_ns.route("/get_stats")
@facial_ns.doc("Get Stats")
class FacialGetStats(Resource):
    @facial_ns.doc("Get Stats")
    @facial_ns.expect(facial_stats)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            stats = facial_func.get_stats(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": stats
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
 
 
            
@facial_ns.route("/get_sop_stats")
@facial_ns.doc("Get SOP Stats")
class FacialGetSopStats(Resource):
    @facial_ns.doc("Get SOP Stats")
    @facial_ns.expect(facial_stats_sop)
    @facial_ns.response(200, "Logs fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            stats = facial_func.get_sop_stats(data)
            
            return {
                "message": "Facial recognition logs fetched successfully.",
                "success": True,
                "data": stats
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
               

@facial_ns.route("/camera_status")
@facial_ns.doc("Get Camera Status")
class FacialGetCameraStatus(Resource):
    @facial_ns.doc("Get Camera Status")
    @facial_ns.expect(cameraStatus)
    @facial_ns.response(200, "Get Cameras successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            cameraStatus = facial_func.camera_status(data)
            
            return {
                "message": "Camera status fetched successfully.",
                "success": True,
                "data": cameraStatus
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
                       
            
@facial_ns.route("/daily_movement_summary")
@facial_ns.doc("Daily Movement Summary")
class FacialGetDailyMovement(Resource):
    @facial_ns.doc("Get Logs")
    @facial_ns.expect(daily_movement_summary_payload)
    @facial_ns.response(200, "Daily Movement Summary fetched successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            # Validate date formats
            patterns = {
                "week": r"\d{4}-W\d{2}",
                "month": r"\d{4}-\d{2}",
                "date": r"\d{4}-\d{2}-\d{2}",
                "starting": r"\d{4}-\d{2}-\d{2}",
                "ending": r"\d{4}-\d{2}-\d{2}"
            }

            f = data.get("filters", {})
            for field, pattern in patterns.items():
                if f.get(field) and not re.match(pattern, f[field]):
                    return {"message": f"Invalid {field} format"}, 400
            
            stats = facial_func.get_daily_movement_summary(data)
            
            return {
                "message": "Daily Movement Summary fetched successfully.",
                "success": True,
                "data": stats
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
              
@facial_ns.route("/sync_logs_count")
@facial_ns.doc("Sync Logs Count")
class FacialSyncLogsCount(Resource):
    @facial_ns.doc("Sync Logs Count")
    @facial_ns.expect(sync_logs_count_item)
    @facial_ns.response(200, "Logs Count Synced successfully.")
    @facial_ns.response(400, "Validation Error")
    def post(self):
        data = facial_ns.payload
        print('Received data----------------', data)
        try:
            # Accept both: single object or {"logs": [...]}
            logs = data["logs"] if "logs" in data and isinstance(data["logs"], list) else [data]
            
            from api.models.facialModels import LogsCount  # Import here to avoid circular import
            
            results = []
            for item in logs:
                # Convert detection_time to date/hour if provided
                if "detection_time" in item:
                    detection_time = item["detection_time"]
                    item["detection_date"] = detection_time.date()
                    item["detection_hour"] = detection_time.hour
                
                result = LogsCount.create_or_update(
                    client_id=item["client_id"],
                    factory_id=item["factory_id"],
                    detection_date=item["detection_date"],
                    detection_hour=item["detection_hour"],
                    total_entry=item.get("total_entry", 0),
                    total_exit=item.get("total_exit", 0)
                )
                results.append(result)
            
            return {
                "message": f"Processed {len(results)} log entries",
                "success": True,
                "updated": len([r for r in results if r["action"] == "updated"]),
                "created": len([r for r in results if r["action"] == "created"])
            }, 200

        except Exception as e:
            print(f"Error in sync_logs_count: {str(e)}")
            abort(400, {"success": False, "message": f"Error: {str(e)}"})
         
            


@facial_ns.route("/update_logs_count")
@facial_ns.doc("Update Logs Count by Date/Hour")
class FacialUpdateLogsCount(Resource):
    @facial_ns.doc("Update Logs Count by Date and Hour")
    @facial_ns.expect(update_logs_count_item)
    @facial_ns.response(200, "Logs Count Updated successfully.")
    @facial_ns.response(400, "Validation Error")
    @facial_ns.response(404, "No matching record found")
    def put(self):
        data = facial_ns.payload
        try:
            # Get required fields
            client_id = data.get("client_id")
            factory_id = data.get("factory_id")
            detection_date = data.get("detection_date")
            detection_hour = data.get("detection_hour")
            
            if not all([client_id, factory_id, detection_date, detection_hour]):
                return {"message": "Missing required fields"}, 400

            # Import here to avoid circular imports
            from api.models.facialModels import LogsCount
            
            # Find and update record by date/hour instead of ID
            updated = LogsCount.update_counts_by_datetime(
                client_id=client_id,
                factory_id=factory_id,
                detection_date=detection_date,
                detection_hour=detection_hour,
                data=data
            )
            
            if updated:
                return {
                    "message": "Logs Count updated successfully.",
                    "success": True
                }, 200
            else:
                return {
                    "message": "No matching record found for the given date/hour",
                    "success": False
                }, 404

        except Exception as e:
            print(f"Error updating logs: {str(e)}")
            abort(400, {"success": False, "message": f"Error: {str(e)}"})
            
            


@facial_ns.route("/get_logs_count")
@facial_ns.doc("Get Logs Count")
class FacialGetLogsCount(Resource):
    @facial_ns.doc("Get logs Count")
    @facial_ns.expect(get_logs_count)
    @facial_ns.response(200, "Get Cameras successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            logs = LogsCount.getLogs(data)
            
            return {
                "message": "Live Logs fetched successfully.",
                "success": True,
                "data": logs
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
            
            
@facial_ns.route("/update_log_verification")
@facial_ns.doc('Update Log')
class FacialLogUpdate(Resource):
    @facial_ns.doc("Update Log")
    @facial_ns.expect(update_log_payload)
    @facial_ns.response(200, "Update Log Successfully")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        log_id = data.get("log_id")
        verified = data.get("verified")
        client = data.get("client")
        if not log_id:
            return {"message": "log_id is required."}, 400

        log = FacialRecognitionLogs.get_by_log_id(log_id=log_id)
        if not log:
            return {"message": "Log not found."}, 400

        if client is True:
            log.client_verified = verified
        else:
            log.ai_verified = verified

        try:
            db.session.commit()
            return {
                "message": "Log updated successfully.",
                "success": True
            }, 200
        except Exception as e:
            db.session.rollback()
            return {
                "message": "Failed to update log.",
                "success": False,
                "error": str(e)
            }, 400
            
            


@facial_ns.route("/get_all_employes")
@facial_ns.doc("Get Employes")
class FacialGetAllEmployees(Resource):
    @facial_ns.doc("Get All Employes")
    @facial_ns.expect(getEmployes)
    @facial_ns.response(200, "Get Employes successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        try:
            req_fields = ["client_id", "factory_id"]
            for field in req_fields:
                if field not in data or not data[field]:
                    return {"message": f"{field} is missing."}, 400
            
            facialEmployes = facial_func.get_employes(data)
            
            return {
                "message": "Employes fetched successfully.",
                "success": True,
                "data": facialEmployes
            }, 200
            
        except Exception as e:
            print(e)
            abort(400, {"success": False, "message": f"Error {e}"})
                       

@facial_ns.route("/blacklist_toggle")
@facial_ns.doc("Toggle Blacklist Status")
class BlacklistToggle(Resource):
    @facial_ns.expect(blacklist_toggle_payload)
    @facial_ns.response(200, "Blacklist status updated successfully.")
    @facial_ns.response(400, "Validation Error")
    def put(self):
        data = facial_ns.payload
        employee_id = data.get("employee_id")
        blacklisted = data.get("blacklisted")

        if employee_id is None or blacklisted is None:
            return {"message": "employee_id and blacklisted are required."}, 400

        emp = FacialEmployees.query.filter_by(employee_id=employee_id).first()
        if not emp:
            return {"message": "Employee not found."}, 400

        emp.blacklisted = blacklisted
        try:
            db.session.commit()
            return {
                "message": f"Employee {employee_id} blacklist status set to {blacklisted}.",
                "success": True
            }, 200
        except Exception as e:
            db.session.rollback()
            return {
                "message": "Failed to update blacklist status.",
                "success": False,
                "error": str(e)
            }, 400                    
            
