from api import surveillance_ns
from flask_restx import fields

# Payload model for inserting interval images
surveillance_insert_interval_images_payload_model = surveillance_ns.model(
    "SurveillanceInsertIntervalImagesPayload",
    {
        "images": fields.List(
            fields.Nested(
                surveillance_ns.model(
                    "ImageData",
                    {
                        "camera_id": fields.String(
                            required=True,
                            description="ID of the camera",
                            default="ICF-AO7-BDE-1-6-10-116",
                        ),
                        "client_id": fields.Integer(
                            required=True, description="Client ID", default=1
                        ),
                        "factory_id": fields.Integer(
                            required=True, description="Factory ID", default=1
                        ),
                        "image_url": fields.String(
                            required=True, description="URL of the image"
                        ),
                        "timestamp": fields.String(
                            required=True,
                            description="Timestamp of the image",
                            default="2024-10-15 20:41:02",
                        ),
                    },
                )
            )
        )
    },
)

# Define models for the nested data
SurveillanceStaffEvents_model = surveillance_ns.model(
    "SurveillanceStaffEvents",
    {
        
        "id": fields.Integer(required=True, description="Local db record ID"),

        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
        "is_present": fields.Boolean(
            required=True, description="Attendance", default=False
        ),
        "machine_id": fields.Integer(
            required=True, description="Machine ID", default=0
        ),
        "image_path": fields.String(
            required=True, description="URL of the image"
        ),
        "timestamp": fields.String(
                            required=True,
                            description="Timestamp of the image",
                            default="2024-10-15 20:41:02",
                        ),
    },
)

SurveillanceCustomerCounts_model = surveillance_ns.model(
    "SurveillanceCustomerCounts",
    {
        
        "id": fields.Integer(required=True, description="Local db record ID"),

        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
        "count": fields.Integer(
            required=True, description="Customer count", default=0
        ),
        "image_path": fields.String(
            required=True, description="URL of the image"
        ),
        "timestamp": fields.String(
                            required=True,
                            description="Timestamp of the image",
                            default="2024-10-15 20:41:02",
                        ),
    },
)

# Define the main payload model
surveillance_sync_payload_model = surveillance_ns.model(
    "Surveillance Sync Payload",
    {
        "surveillance_staff_events": fields.List(
            fields.Nested(SurveillanceStaffEvents_model),
            required=True,
            description="List of Staff event records",
        ),
        "surveillance_customer_counts": fields.List(
            fields.Nested(SurveillanceCustomerCounts_model),
            required=True,
            description="List of Customer count records",
        )
    },
)


# -------------------------------- General Payload for filters ----------------------------
filters_payload = surveillance_ns.model(
    "Payload with all filters",
    {

        "filters": fields.Nested(surveillance_ns.model('Vehicle Filters', {
            "date": fields.String(default="2025-04-20", description="Date - format (YYYY-MM-DD)"),
            "week": fields.String(default="", description="Week - format (YYYY-WWW)"),
            "month": fields.String(default="", description="Month - format (YYYY-MM)"),
            "starting": fields.String(default="", description="Starting - format (YYYY-MM-DD)"),
            "ending": fields.String(default="", description="Ending - format (YYYY-MM-DD)"),
            "shift": fields.String(default="", description="Shift"),            
        }), description="Filters for Total Entry Exit Count"),

        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"

        ),
        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        )
    },
)


# --------------------------- For Monitoring Feed --------------------------------

monitoring_feed_payload = surveillance_ns.model(
    "Monitoring Feed Payload",
    {
        "client_id": fields.Integer(
            required=True, description="Client ID", default=1
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID", default=1
        ),
    },
)

hourly_customer_traffic_payload = surveillance_ns.model(
    "Hourly Customer Traffic",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="The associated Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The associated Factory ID"
        ),
        "filters": fields.Nested(surveillance_ns.model('Traffic Filters', {
            "date": fields.String(default="2025-04-20", description="Date - format (YYYY-MM-DD)"),
            "week": fields.String(default="", description="Week - format (YYYY-WWW)"),
            "month": fields.String(default="", description="Month - format (YYYY-MM)"),
        }), description="Filters for Customer Traffic"),
    }
)

staff_presence_timeline_payload = surveillance_ns.model(
    "StaffPresenceTimelineRequest",
    {
        "factory_id": fields.Integer(
            required=True,
            example=1,
            description="The ID of the factory to get staff presence data for"
        ),
        "filters": fields.Nested(
            surveillance_ns.model('TimelineFilters', {
                "date": fields.String(
                    required=False,
                    default="2025-04-20",
                    description="Get data for a specific date (YYYY-MM-DD format)"
                ),
                "week": fields.String(
                    required=False,
                    default="2025-W16",
                    description="Get data for the week containing this date (YYYY-MM-DD format)"
                ),
                "month": fields.String(
                    required=False,
                    default="2025-04",
                    description="Get data for the month containing this date (YYYY-MM-DD format)"
                )
            }),
            required=True,
            description="Time period filter (must provide exactly one of date/week/month)"
        )
    }
)
