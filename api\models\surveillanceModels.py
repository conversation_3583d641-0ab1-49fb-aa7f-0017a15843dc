from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
import datetime
from sqlalchemy import desc, func


class SurveillanceLiveCameraImages(db.Model):
    __tablename__ = 'surveillance_live_camera_images'
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.<PERSON>ey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.Foreign<PERSON>ey('ast_factory.factory_id'))
    camera_id = db.Column(db.Text, db.ForeignKey('ast_cameras.camera_id'))
    image_url = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    
    @classmethod
    def add_images_in_interval(cls, data):
        """
        Adds multiple images for different cameras at a specific interval.
        :param data: List of dictionaries containing camera_id and image_url
        Example:
        [
            {"camera_id": 1, "image_url": "http://example.com/image1.jpg", "timestamp":"2024-10-15 20:41:02"},
            {"camera_id": 2, "image_url": "http://example.com/image2.jpg", "timestamp":"2024-10-15 20:41:02"}
        ]
        """
        rows = []
        not_found=[]
        for item in data:
            camera_id = item.get("camera_id")
            client_id = item.get("client_id")
            factory_id = item.get("factory_id")
            image_url = item.get("image_url")
            timestamp = item.get("timestamp")

            existing_record = cls.query.filter((cls.camera_id == camera_id)&(cls.client_id == client_id)&(cls.factory_id == factory_id)).first()

            if existing_record:
                existing_record.image_url=image_url
                existing_record.timestamp=timestamp
                db.session.commit()
            else:
                new_record = cls(
                    client_id=client_id,
                    factory_id=factory_id,
                    camera_id=camera_id,
                    image_url=image_url, 
                    timestamp=timestamp  
                )
                rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
        
        return not_found
    
class SurveillanceStaffEvents(db.Model):
    __tablename__ = 'surveillance_staff_events'
    id = db.Column(db.Integer, primary_key=True)
    local_db_id = db.Column(db.Integer)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    is_present = db.Column(db.Boolean, default=False, nullable=False)
    machine_id = db.Column(db.Integer, default=0, nullable=False)
    image_path = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row_id= cls.query.filter((cls.client_id == client_id)&(cls.factory_id == factory_id)).order_by(desc(cls.local_db_id)).first()
        return latest_row_id.local_db_id if latest_row_id else None
        
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            new_record = cls(
                local_db_id=data.get("id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                is_present=data.get("is_present"),
                machine_id=0 if data.get("machine_id") is None else data["machine_id"],
                image_path=data.get("image_path"),
                timestamp=data.get("timestamp")
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False
        
    
class SurveillanceCustomerCounts(db.Model):
    __tablename__ = 'surveillance_customer_counts'
    id = db.Column(db.Integer, primary_key=True)
    local_db_id = db.Column(db.Integer)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    count = db.Column(db.Integer, default=0, nullable=False)
    image_path = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row_id= cls.query.filter((cls.client_id == client_id)&(cls.factory_id == factory_id)).order_by(desc(cls.local_db_id)).first()
        return latest_row_id.local_db_id if latest_row_id else None
        
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            new_record = cls(
                local_db_id=data.get("id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                count=data.get("count"),
                image_path=data.get("image_path"),
                timestamp=data.get("timestamp")
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False
