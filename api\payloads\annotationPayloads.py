from api import annotation_ns, websocket_ns

from flask_restx import fields

# Annotation

get_unannotated_images_payload = annotation_ns.model(
    "Get Unannotated Images",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        ),
        "limit": fields.Integer(
            default=10, required=True, description="Limit of images"
        ),
        "job_id": fields.Integer(
            default=1, required=True, description="Job ID"
        )
    },
)

get_review_images_payload = annotation_ns.model(
    "Get Review Images",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        ),
        "annotation_user_id": fields.Integer(
            default=1, required=True, description="The associated Annotation User ID"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        ),
        "limit": fields.Integer(
            default=10, required=True, description="Limit of images"
        ),
        "job_id": fields.Integer(
            default=1, required=True, description="Job ID"
        )
    },
)

annotated_image_payload = annotation_ns.model(
    "Annotated Image",
    {
        "id": fields.Integer(
            default=1, required=True, description="The associated ID"
        ),
        "annotation": fields.String(
            default="[]", description="Annotation"
        ),
        "disqualified": fields.Boolean(
            default=False, required=True, description="Image disqualification"
        )
    },
)

# Define the main payload model
submit_annotated_images_payload = annotation_ns.model(
    "Annotation",
    {
        "annotation_list": fields.List(
            fields.Nested(annotated_image_payload),
            required=True,
            description="List of annotated image records",
        )
    },
)

reviewed_image_payload = annotation_ns.model(
    "Reviewed Image",
    {
        "id": fields.Integer(
            default=1, required=True, description="The associated ID"
        ),
        "review_comments": fields.String(
            default="Reason of annotation rejection", description="Reviewer comments about annotation rejection."
        ),
        "rejected": fields.Boolean(
            default=False, required=True, description="Annotation rejected"
        )
    },
)

# Define the main payload model
submit_reviewed_images_payload = annotation_ns.model(
    "Reviewed",
    {
        "reviewed_list": fields.List(
            fields.Nested(reviewed_image_payload),
            required=True,
            description="List of reviewed image records",
        )
    },
)



get_annotation_summary_payload = annotation_ns.model(
    "Get Annotation Summary",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        )
    },
)


get_dataset_summary_payload = annotation_ns.model(
    "Get Dataset Summary",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        )
    },
)


generate_dataset_payload = annotation_ns.model(
    "Generate Dataset",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        )
    },
)


# Payload model for inserting interval images
insert_interval_images_payload_model = annotation_ns.model(
    "InsertIntervalImagesPayload",
    {
        "images": fields.List(
            fields.Nested(
                annotation_ns.model(
                    "ImageData",
                    {
                        "camera_id": fields.Integer(
                            required=True,
                            description="ID of the camera",
                        ),
                        "image_url": fields.String(
                            required=True, description="URL of the image"
                        ),
                        "timestamp": fields.String(
                            required=True,
                            description="Timestamp of the image",
                            default="2024-10-15 20:41:02",
                        ),
                    },
                )
            )
        )
    },
)


annotation_camera_image_payload = annotation_ns.model(
    "Camera Image",
    {
        "camera_id": fields.String(
            default="CAM-001-ATD-4", description="The associated Camera ID"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        ),
        "image_url": fields.String(
            default="Image URL", description="The associated Image URL"
        ),
        "detection_model": fields.String(
            default="yolo", description="The associated detection model"
        )
    },
)

# Define the main payload model
annotation_camera_images_payload_model = annotation_ns.model(
    "Camera Images Payload",
    {
        "images": fields.List(
            fields.Nested(annotation_camera_image_payload),
            required=True,
            description="List of camera images",
        )
    },
)

delete_camera_image_payload = annotation_ns.model(
    "Delete Camera Image",
    {
        "image_id": fields.Integer(
            required=True, description="The ID of the image to be deleted"
        )
    },
)

yolo_classes_payload = annotation_ns.model(
    "Add Yolo Classes",
    {
        "client_id": fields.Integer(required=True, description="Client ID"),
        "factory_id": fields.Integer(required=True, description="Factory ID"),
        "modules": fields.List(fields.Integer, required=True, description="List of Module IDs"),
        "batch_id": fields.Integer(default=1, required=True, description="Batch ID"),
    }
)

get_yolo_classes_payload = annotation_ns.model(
    "Get Yolo Classes", 
    {
    'client_id': fields.Integer(default=1,required=True, description='Client ID'),
    'factory_id': fields.Integer(default=1,required=True, description='Factory ID'),
    'batch_id': fields.Integer(default=1, required=True, description="Batch ID"),
    }
)


get_user_summary_payload = annotation_ns.model(
    "Get User Summary",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        )
    },
)

get_user_monthly_chart_payload = annotation_ns.model(
    "Get User Monthly Chart",
    {
        "user_id": fields.Integer(
            default=1, required=True, description="The associated User ID"
        )
    },
)


request_unannotated_images_payload = annotation_ns.model(
    "Request Unannotated Images",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        ),
        "camera_ids": fields.List(
            fields.String(default="", description="List of Camera ID")
        ),
        "images_count": fields.Integer(
            default=10, required=True, description="Image count"
        ),
        "skip_frames": fields.Integer(
            default=0, description="Images Interval"
        ),
        "background_images": fields.Boolean(
            default=False, description="Background Image required"
        )
    },
)

get_user_progress_payload = annotation_ns.model(
    "Get all images for porgress",
    {
        "status": fields.String(
            default="", required=False, description="Image status: annotated, unannotated, rejected, disqualified"
        ),
        "annotation_user_id": fields.Integer(
            required=False, description="Filter by User ID"
        ),
        "factory_id": fields.Integer(
            required=False, description="Filter by Factory ID"
        ),
        "page": fields.Integer(
            default=1, required=False, description="Page number"
        ),
        "per_page": fields.Integer(
            default=20, required=False, description="Number of records per page"
        )
    },
)

assign_annotation_job_payload = annotation_ns.model(
    "Assign Annotation Job",
    {
        "client_id": fields.Integer(
            default=1, required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="Factory ID"
        ),
        "batch_id": fields.Integer(
            default=1, required=True, description="Batch ID"
        ),
        "user_id": fields.Integer(
            default=1, required=True, description="User ID"
        ),
        "limit": fields.Integer(
            default=10, required=True, description="Limit of images"
        ),
    },
)

transfer_annotation_job_payload = annotation_ns.model(
    "Transfer Annotation Job",
    {
        "job_id": fields.Integer(
            required=True, description="Job ID"
        ),
        "user_id": fields.Integer(
            required=True, description="User ID"
        )
    },
)

get_all_annotation_jobs_payload = annotation_ns.model(
    "Get All Annotation Jobs",
    {
        "user_id": fields.Integer(
            required=False, description="User ID"
        ),
        "filters": fields.Nested(
            annotation_ns.model('Annotation Jobs Filters', {
                "starting": fields.String(default="", description="Starting - format (YYYY-MM-DD)"),
                "ending": fields.String(default="", description="Ending - format (YYYY-MM-DD)"),
                "date": fields.String(default="", description="Date - format (YYYY-MM-DD)"),
                "week": fields.String(default="", description="Week - format (YYYY-WWW)"),
                "month": fields.String(default="", description="Month - format (YYYY-MM)"),
                "factory_id": fields.Integer(default=0, description="Factory ID"),
                "client_id": fields.Integer(default=0, description="Client ID"),
                "batch_id": fields.Integer(default=0, description="Batch ID"),
                "status": fields.String(default="", description="Status - pending, in progress, completed"),
                "limit": fields.Integer(default=0, description="Limit of images"),
            }), description="Filters for Annotation Jobs"
        )
    },
)

update_annotation_job_status_payload = annotation_ns.model(
    "Update Annotation Job Status",
    {
        "job_id": fields.Integer(
            required=True, description="Job ID"
        ),
        "status": fields.String(
            required=True, description="Status - pending, in progress, completed"
        )
    },
)

get_training_image_counts_per_camera_payload = annotation_ns.model(
    "Get Camera Image Counts by Dataset",
    {
        "client_id": fields.Integer(
            required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID"
        ),
    },
)

insert_false_alert_images_payload_model = annotation_ns.model(
    "Insert False Alert Images",
    {
        "images": fields.List(
            fields.Nested(
                annotation_ns.model(
                    "Images",
                    {   
                        "camera_id": fields.String(
                            required=True, description="Camera ID", default="CAM-001-ATD-4"
                        ),
                        "client_id": fields.Integer(
                            required=True, description="Client ID", default=1
                        ),
                        "factory_id": fields.Integer(
                            required=True, description="Factory ID", default=1
                        ),
                        "module": fields.String(
                            required=True, description="Module", default="Helmet"
                        ),
                        "image_url": fields.String(
                            required=True, description="Image URL", default="https://image.url"
                        ),
                        "detection_model": fields.String(
                            required=True, description="Detection Model", default="yolo"
                        )
                    },
                )
            )
        )
    },
)