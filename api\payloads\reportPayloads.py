from api import report_ns
from flask_restx import fields

send_mail_payload = report_ns.model(
    "Send Mail",
    {
        "receivers": fields.List(
            fields.String,
            required=True,
            description="List of recipients' email addresses.",
        ),
        "cc_emails": fields.List(
            fields.String,
            required=False,
            description="List of CC email addresses.",
        ),
        "subject": fields.String(
            required=True,
            description="Email subject.",
        ),
        "body": fields.String(
            required=True,
            description="Email body content.",
        ),
    },
)


get_tickets_payload = report_ns.model(
    "Get Tickets",
    {
        "user_id": fields.Integer(
            required=True, description="User ID"
        ),
        "client_id": fields.Integer(
            required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID"
        ),
    },
)

get_tickets_by_client_factory_payload = report_ns.model(
    "Get Tickets by Client, Factory",
    {
        "client_id": fields.Integer(
            required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID"
        ),
    },
)

create_uodate_ticket_payload = report_ns.model(
    "Create Update Ticket",
    {
        "ticket_id": fields.Integer(
            required=False, description="Ticket ID"
        ),
        "generated_by": fields.Integer(
            required=True, description="User ID"
        ),
        "client_id": fields.Integer(
            required=True, description="Client ID"
        ),
        "factory_id": fields.Integer(
            required=True, description="Factory ID"
        ),
        "priority": fields.String(
            required=True, description="Priority"
        ),
        "query": fields.String(
            required=True, description="Query"
        ),
        "comment": fields.String(
            required=False, description="Comment"
        ),
    },
)

toggle_ticket_status_payload = report_ns.model(
    "Toggle Ticket Status",
    {
        "ticket_id": fields.Integer(
            required=True, description="Ticket ID"
        ),
        "status": fields.String(
            required=True, description="Status"
        ),
        "comment": fields.String(
            required=False, description="Comment"
        ),
    },
)
