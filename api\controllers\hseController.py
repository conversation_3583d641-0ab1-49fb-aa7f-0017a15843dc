from collections import defaultdict
from flask_restx import Resource, abort
from sqlalchemy import func, cast, Date, case, Integer

from api.models.uamModels import *
from api.models.assetModels import *
from api.models.hseModels import *


from api import hse_ns
from api.payloads.hsePayloads import *
from api.services import token_required

from api.controllers.hseController import *
from api.utils.utils import *

from api.utils.utils import parse_filters, to_local
import datetime
import traceback
import re
from zoneinfo import ZoneInfo
from datetime import timedelta, timezone
import calendar



# ╔════════════════════════════════╗
# ║ UNIFIED APIs START             ║
# ╚════════════════════════════════╝


def get_filtered_alerts(client_id, factory_id, start_date, end_date):
    """
    Returns a SQLAlchemy query object filtered by client, factory, and date range.
    """
    try:
        print("client_id",client_id)
        print("factory_id",factory_id)
        print(start_date)
        print(end_date)
        return (
            db.session.query(Alerts)
            .filter(
                Alerts.client_id == client_id,
                Alerts.factory_id == factory_id,
                Alerts.timestamp >= start_date,
                Alerts.timestamp <= end_date,
                Alerts.compliant == False
            )
        )
    except Exception as e:
        print("Error in get_filtered_alerts:", e)
        return db.session.query(Alerts).filter(False)  # Return an empty query


def get_alert_count_by_module(client_id, factory_id, start_date, end_date):
    """
    Returns a list of dictionaries with alert counts grouped by module type.

    Args:
        client_id (int): The client ID
        factory_id (int): The factory ID
        start_date (datetime): Start datetime for filtering
        end_date (datetime): End datetime for filtering

    Returns:
        List[Dict]: Each dict contains module_id, module_name, and alert_count
    """
    try:
        # Reuse the filtered query
        filtered_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)

        # Count grouped by module_id
        alert_counts = (
            filtered_query
            .with_entities(
                Alerts.module_id,
                func.count(Alerts.alert_id).label("alert_count")
            )
            .group_by(Alerts.module_id)
            .all()
        )

        # Map module_id to module name
        module_map = {m.module_id: m.name for m in Modules.get_all_records()}

        result = []
        for module_id, count in alert_counts:
            result.append({
                "module_id": module_id,
                "module_name": module_map.get(module_id, "Unknown"),
                "alert_count": count
            })

        return result

    except Exception as e:
        print("Error in get_alert_count_by_module:", e)
        return []
    

def get_alert_count_by_zone(client_id, factory_id, start_date, end_date):
    """
    Returns alert counts by zone_id, enriched with zone names.
    """
    try:
        # Step 1: Get filtered alerts (reusing helper)
        filtered_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)

        # Step 2: Group alerts by zone_id
        alert_counts = (
            filtered_query
            .with_entities(
                Alerts.zone_id,
                func.count(Alerts.alert_id).label("alert_count")
            )
            .group_by(Alerts.zone_id)
            .all()
        )

        # Step 3: Get zone names for client & factory
        zones = AstZones.get_by_client_factory(client_id, factory_id)
        zone_map = {z.zone_id: z.name for z in zones}

        # Step 4: Build final result
        result = []
        for zone_id, count in alert_counts:
            result.append({
                "zone_id": zone_id if zone_id else 0,
                "zone_name": zone_map.get(zone_id, "Unknown" if zone_id else "Unassigned"),
                "alert_count": count
            })

        return result

    except Exception as e:
        print("Error in get_alert_count_by_zone:", e)
        return []
    

def get_alert_trend_by_module(client_id, factory_id, start_date, end_date):
    """
    Returns alert trends per module per day (for line chart)
    """
    try:
        # Step 1: Get filtered alerts
        filtered_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)

        # Step 2: Group by date and module
        grouped_data = (
            filtered_query
            .with_entities(
                cast(Alerts.timestamp, Date).label("date"),
                Alerts.module_id,
                func.count(Alerts.alert_id).label("count")
            )
            .group_by("date", Alerts.module_id)
            .order_by("date")
            .all()
        )

        # Step 3: Prepare labels (date range)
        date_cursor = start_date.date()
        end_date_only = end_date.date()
        labels = []
        while date_cursor <= end_date_only:
            labels.append(str(date_cursor))
            date_cursor += datetime.timedelta(days=1)

        # Step 4: Prepare module-wise dataset
        module_map = {m.module_id: m.name for m in Modules.get_all_records()}
        trend_data = {}

        for entry in grouped_data:
            date_str = str(entry.date)
            module_id = entry.module_id
            count = entry.count

            if module_id not in trend_data:
                trend_data[module_id] = {
                    "module_id": module_id,
                    "module_name": module_map.get(module_id, "Unknown"),
                    "data": {d: 0 for d in labels}  # Initialize 0s
                }
            trend_data[module_id]["data"][date_str] = count

        # Step 5: Format for line chart
        datasets = []
        for module_id, mod_data in trend_data.items():
            datasets.append({
                "module_id": module_id,
                "module_name": mod_data["module_name"],
                "data": [mod_data["data"][label] for label in labels]
            })

        return {
            "labels": labels,
            "datasets": datasets
        }

    except Exception as e:
        print("Error in get_alert_trend_by_module:", e)
        return {"labels": [], "datasets": []}
    

def get_active_zone_summary(client_id, factory_id):
    """
    Returns total and active zone counts for given client & factory.
    """
    try:
        zones = AstZones.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        total = len(zones)
        active = len([z for z in zones if z.active])
        return {
            "total": total,
            "active": active
        }
    except Exception as e:
        print("Error in get_active_zone_summary:", e)
        return {
            "total": 0,
            "active": 0
        }
    


    
def get_filtered_live_alerts(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        module_ids = data.get("module_ids", [])
        zone_ids = data.get("zone_ids", [])
        area_ids = data.get("area_ids", [])
        sub_area_ids = data.get("sub_area_ids", [])
        pagination = data.get("pagination", {"page_no": 1, "per_page": 20})

        start_date, end_date = parse_filters(data)

        # Base query (compliant=False)
        query = (
            get_filtered_alerts(client_id, factory_id, start_date, end_date)
            .join(Cameras, Alerts.camera_id == Cameras.camera_id)
            .with_entities(
                Alerts,
                Cameras
            )
        )

        # Apply filters
        if module_ids:
            query = query.filter(Alerts.module_id.in_(module_ids))
        if zone_ids:
            query = query.filter(Alerts.zone_id.in_(zone_ids))  
        if area_ids:
            query = query.filter(Cameras.area_id.in_(area_ids))
        if sub_area_ids:
            query = query.filter(Cameras.sub_area_id.in_(sub_area_ids))


        # Apply pagination to the query
        paginated_alerts = query.order_by(Alerts.timestamp.desc()).paginate(
            page=pagination["page_no"], 
            per_page=pagination["per_page"], 
            error_out=False
        )

        alerts = paginated_alerts.items
        print("alerts",alerts)

        # Lookups
        modules = {m.module_id: m.name for m in Modules.get_all_records()}
        zones = {z.zone_id: z.name for z in AstZones.get_by_client_factory(client_id, factory_id)}
        areas = {a.area_id: a.name for a in Area.get_records_by_client_factory(client_id, factory_id)}
        sub_areas = {sa.sub_area_id: sa.name for sa in SubArea.get_records_by_client_factory(client_id, factory_id)}

        client = Client.query.filter_by(client_id=client_id).first()
        factory = Factory.query.filter_by(factory_id=factory_id).first()
        client_name = client.name if client else "Unknown"
        factory_name = factory.name if factory else "Unknown"

        client_timezone = Client.get_timezone_by_id(client_id)   # fetch client
        client_tz = ZoneInfo(client_timezone)  # e.g. "Asia/Karachi"
        
        
            
        result = []
        for alert_data in alerts:
            alert = alert_data[0] 
            camera = alert_data[1]
            
            local_timestamp = to_local(alert.timestamp, client_tz)
            local_updated_at = to_local(alert.updated_at, client_tz)
        
            result.append({
                "alert_id": alert.alert_id,
                "client_id": alert.client_id,
                "client_name": client_name,
                "factory_id": alert.factory_id,
                "factory_name": factory_name,
                "module_id": alert.module_id,
                "module_name": modules.get(alert.module_id, "Unknown"),
                "zone_id": alert.zone_id,
                "zone_name": zones.get(alert.zone_id, "Unknown") if alert.zone_id else "Unassigned",
                "camera_id": alert.camera_id,
                "camera_name": camera.camera_name,
                "area_id": camera.area_id,
                "area_name": areas.get(camera.area_id, "Unknown") if camera.area_id else "Unassigned",
                "sub_area_id": camera.sub_area_id,
                "sub_area_name": sub_areas.get(camera.sub_area_id, "Unknown") if camera.sub_area_id else "Unassigned",
                "compliant": alert.compliant,
                "unannotated_url": alert.unannotated_url,
                "annotated_url": alert.annotated_url,
                "techqa": alert.techqa,
                "aitechqa": alert.aitechqa,
                "timestamp": local_timestamp.isoformat(),
                "updated_at": local_updated_at.isoformat(),
                "local_db_id": alert.local_db_id
            })

        return {
            "alerts": result,
            "pagination": {
                "current_page": paginated_alerts.page,
                "per_page": paginated_alerts.per_page,
                "total_pages": paginated_alerts.pages,
                "total_records": paginated_alerts.total,
                "has_next": paginated_alerts.has_next,
                "has_prev": paginated_alerts.has_prev
            }
        }

    except Exception as e:
        print("Error in get_filtered_live_alerts:", e)
        return []
    

def get_alert_trend(data):
    try:
        import datetime

        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        filter_type = data.get("filter_type", "week")  # "monthly", "date", "weekly"
        start_date, end_date = parse_filters(data)

        # Module name mapping (lowercase)
        modules = Modules.get_all_records()
        module_name_map = {m.name.lower(): m.module_id for m in modules}

        module_keys = {
            "unauthorized": ["unauthorize", "unauthorized", "unathorise_access"],
            "ppe": ["ppe"],
            "intruder": ["intruder"],
            "work_permit": ["work permit", "work_permit", "workpermit"]
        }

        # Find module_ids for each key
        module_ids_map = {}
        for key, names in module_keys.items():
            for name in names:
                mod_id = module_name_map.get(name)
                if mod_id:
                    module_ids_map[key] = mod_id
                    break

        # Prepare time buckets
        if filter_type == "month":
            # Per day of month
            days = []
            date_cursor = start_date.date()
            while date_cursor <= end_date.date():
                days.append(date_cursor)
                date_cursor += datetime.timedelta(days=1)
            labels = [str(d) for d in days]
            bucket_func = lambda alert: str(alert.timestamp.date())
        elif filter_type == "date":
            # Per hour of the day
            labels = [f"{h:02d}:00" for h in range(24)]
            bucket_func = lambda alert: f"{alert.timestamp.hour:02d}:00"
        elif filter_type == "week":
            # Monday to Sunday
            labels = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            bucket_func = lambda alert: alert.timestamp.strftime("%A")
        else:
            return {"labels": [], "datasets": []}

        # Query all relevant alerts in one go
        base_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)
        alerts = base_query.filter(
            Alerts.module_id.in_(list(module_ids_map.values()))
        ).all()

        # Prepare result structure
        datasets = []
        for key in ["unauthorized", "ppe", "intruder", "work_permit"]:
            mod_id = module_ids_map.get(key)
            if not mod_id:
                datasets.append({"name": key, "data": [0] * len(labels)})
                continue

            # Filter alerts for this module
            mod_alerts = [a for a in alerts if a.module_id == mod_id]
            # Count per bucket
            count_map = {label: 0 for label in labels}
            for alert in mod_alerts:
                bucket = bucket_func(alert)
                if bucket in count_map:
                    count_map[bucket] += 1
            datasets.append({
                "name": key,
                "data": [count_map[label] for label in labels]
            })

        return {
            "labels": labels,
            "datasets": datasets
        }
    except Exception as e:
        print("Error in get_alert_trend:", e)
        return {"labels": [], "datasets": []}


def get_alert_trend_analytics(data):
    """
    Dynamic alert trend analytics API that allows users to specify X-axis dimension
    Supported dimensions: modules, zones, areas, subareas
    Y-axis is always alert counts
    """
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        x_axis = data.get("x_axis", "modules")  # Default to modules if not specified
        module_ids = data.get("module_ids", [])
        zone_ids = data.get("zone_ids", [])
        area_ids = data.get("area_ids", [])
        sub_area_ids = data.get("sub_area_ids", [])
        
        # Validate x_axis parameter
        valid_dimensions = ["modules", "zones", "areas", "subareas"]
        if x_axis not in valid_dimensions:
            return {
                "error": f"Invalid x_axis value. Supported: {valid_dimensions}",
                "series": [],
                "categories": []
            }
        
        start_date, end_date = parse_filters(data)
        
        # Get filter type from data
        filters = data.get("filters", {})
        
        # Determine categories and bucket function based on filter type
        if filters.get("date"):
            categories = [str(h) for h in range(24)]
            get_bucket = lambda alert: str(alert.timestamp.hour)
        elif filters.get("week"):
            categories = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
            get_bucket = lambda alert: categories[alert.timestamp.weekday()]
        elif filters.get("month"):
            days_in_month = (end_date - start_date).days + 1
            categories = [str(i) for i in range(1, days_in_month + 1)]
            get_bucket = lambda alert: str(alert.timestamp.day)
        else:
            categories = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
            get_bucket = lambda alert: categories[alert.timestamp.weekday()]
        
        # Base query with filters
        query = get_filtered_alerts(client_id, factory_id, start_date, end_date)
        
        if module_ids:
            query = query.filter(Alerts.module_id.in_(module_ids))
        if zone_ids:
            query = query.filter(Alerts.zone_id.in_(zone_ids))

        # Join with Cameras if needed for areas/subareas
        join_needed = False
        if area_ids or sub_area_ids or x_axis in ["areas", "subareas"]:
            join_needed = True
            query = query.join(Cameras, Alerts.camera_id == Cameras.camera_id)
            
            if area_ids:
                query = query.filter(Cameras.area_id.in_(area_ids))
            if sub_area_ids:
                query = query.filter(Cameras.sub_area_id.in_(sub_area_ids))
        
        alerts = query.all()
        
        # Get dimension items based on x_axis and filters
        if x_axis == "modules":
            factory_modules = Factory_Modules.get_modules_by_factory_id(factory_id)
            if module_ids:
                dimension_items = [(m["module_id"], m["name"]) for m in factory_modules if m["module_id"] in module_ids]
            else:
                dimension_items = [(m["module_id"], m["name"]) for m in factory_modules] if factory_modules else []
                
        elif x_axis == "zones":
            all_zones = AstZones.get_by_client_factory(client_id, factory_id)
            if zone_ids:
                dimension_items = [(z.zone_id, z.name) for z in all_zones if z.zone_id in zone_ids]
            else:
                dimension_items = [(z.zone_id, z.name) for z in all_zones]
                
        elif x_axis == "areas":
            all_areas = Area.get_records_by_client_factory(client_id, factory_id)
            if area_ids:
                dimension_items = [(a.area_id, a.name) for a in all_areas if a.area_id in area_ids]
            else:
                dimension_items = [(a.area_id, a.name) for a in all_areas]
                
        elif x_axis == "subareas":
            all_subareas = SubArea.get_records_by_client_factory(client_id, factory_id)
            if sub_area_ids:
                dimension_items = [(sa.sub_area_id, sa.name) for sa in all_subareas if sa.sub_area_id in sub_area_ids]
            else:
                dimension_items = [(sa.sub_area_id, sa.name) for sa in all_subareas]
        
        # Initialize series
        series = []
        total_counts = {category: 0 for category in categories}
        
        # Process each dimension item
        for dim_id, dim_name in dimension_items:
            dim_counts = {category: 0 for category in categories}
            
            for alert in alerts:
                alert_belongs = False
                
                if x_axis == "modules":
                    alert_belongs = (alert.module_id == dim_id)
                elif x_axis == "zones":
                    alert_belongs = (alert.zone_id == dim_id)
                elif x_axis == "areas" and join_needed:
                    camera_query = db.session.query(Cameras).filter(Cameras.camera_id == alert.camera_id).first()
                    if camera_query:
                        alert_belongs = (camera_query.area_id == dim_id)
                elif x_axis == "subareas" and join_needed:
                    camera_query = db.session.query(Cameras).filter(Cameras.camera_id == alert.camera_id).first()
                    if camera_query:
                        alert_belongs = (camera_query.sub_area_id == dim_id)
                
                if alert_belongs:
                    bucket = get_bucket(alert)
                    if bucket in dim_counts:
                        dim_counts[bucket] += 1
                        total_counts[bucket] += 1
            
            series.append({
                "name": dim_name,
                "data": [dim_counts[category] for category in categories]
            })
        
        # Add total series
        series.append({
            "name": "Total", 
            "data": [total_counts[category] for category in categories]
        })
        
        return {
            "x_axis": x_axis,
            "series": series,
            "categories": categories
        }
        
    except Exception as e:
        print("Error in get_alert_trend_analytics:", e)
        return {
            "error": f"Error generating trend analytics: {str(e)}",
            "x_axis": data.get("x_axis", "modules"),
            "series": [],
            "categories": []
        }


def get_overall_compliance_score(data):
    """
    Calculate overall compliance score using the formula:
    Compliance (%) = (Number of Non Compliant Checks ÷ (Compliant Checks + Non-Compliant Checks)) × 100
    """
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        
        # Get the flag to determine grouping (zone, area, or sub_area)
        breakdown = data.get("breakdown")
        if not breakdown or breakdown.strip() == "":
            breakdown = "zone"  # default to zone
        
        start_date, end_date = parse_filters(data)
        
        # Get factory modules
        factory_modules = Factory_Modules.get_modules_by_factory_id(factory_id)
        
        if not factory_modules:
            return {
                "overall_compliance_score": 0,
                "module_compliance_data": [],
                "summary": {
                    "total_non_compliant": 0,
                    "total_compliant": 0,
                    "total_alerts": 0
                }
            }
        
        factory_module_ids = [fm["module_id"] for fm in factory_modules]
        
        # Get zones for client and factory
        zones = AstZones.get_by_client_factory(client_id, factory_id)
        zone_map = {z.zone_id: z.name for z in zones}
        
        # Base query for all alerts (both compliant and non-compliant) filtered by factory modules
        base_query = (
            db.session.query(Alerts)
            .filter(
                Alerts.client_id == client_id,
                Alerts.factory_id == factory_id,
                Alerts.timestamp >= start_date,
                Alerts.timestamp <= end_date,
                Alerts.module_id.in_(factory_module_ids)  # Filter by factory modules
            )
        )
        
        # Get module-wise compliance data using SUM and CAST
        module_compliance_data = (
            base_query
            .with_entities(
                Alerts.module_id,
                func.sum(cast(Alerts.compliant == False, Integer)).label("non_compliant_count"),
                func.sum(cast(Alerts.compliant == True, Integer)).label("compliant_count"),
                func.count(Alerts.alert_id).label("total_alerts")
            )
            .group_by(Alerts.module_id)
            .all()
        )
        
        # Get module-wise alert counts based on grouping flag
        if breakdown == "zone":
            # Get module-zone wise alert counts
            module_group_counts = (
                base_query
                .with_entities(
                    Alerts.module_id,
                    Alerts.zone_id,
                    func.count(Alerts.alert_id).label("group_alert_count")
                )
                .group_by(Alerts.module_id, Alerts.zone_id)
                .all()
            )
            
            # Create group map for zones
            group_map = zone_map
            group_id_field = "zone_id"
            group_name_field = "zone_name"
            
        elif breakdown == "area":
            # Get module-area wise alert counts (need to join with Cameras table)
            module_group_counts = (
                base_query
                .join(Cameras, Alerts.camera_id == Cameras.camera_id)
                .with_entities(
                    Alerts.module_id,
                    Cameras.area_id,
                    func.count(Alerts.alert_id).label("group_alert_count")
                )
                .group_by(Alerts.module_id, Cameras.area_id)
                .all()
            )
            
            # Get areas for mapping
            areas = Area.get_records_by_client_factory(client_id, factory_id)
            group_map = {a.area_id: a.name for a in areas}
            group_id_field = "area_id"
            group_name_field = "area_name"
            
        elif breakdown == "subarea":
            # Get module-sub_area wise alert counts (need to join with Cameras table)
            module_group_counts = (
                base_query
                .join(Cameras, Alerts.camera_id == Cameras.camera_id)
                .with_entities(
                    Alerts.module_id,
                    Cameras.sub_area_id,
                    func.count(Alerts.alert_id).label("group_alert_count")
                )
                .group_by(Alerts.module_id, Cameras.sub_area_id)
                .all()
            )
            
            # Get sub areas for mapping
            sub_areas = SubArea.get_records_by_client_factory(client_id, factory_id)
            group_map = {sa.sub_area_id: sa.name for sa in sub_areas}
            group_id_field = "sub_area_id"
            group_name_field = "sub_area_name"
            
        else:
            # Default to zone if invalid flag
            module_group_counts = (
                base_query
                .with_entities(
                    Alerts.module_id,
                    Alerts.zone_id,
                    func.count(Alerts.alert_id).label("group_alert_count")
                )
                .group_by(Alerts.module_id, Alerts.zone_id)
                .all()
            )
            
            group_map = zone_map
            group_id_field = "zone_id"
            group_name_field = "zone_name"

        
        # Create module map from factory modules
        module_map = {fm["module_id"]: fm["name"] for fm in factory_modules}
        
        # Find max alerts group for each module
        module_max_groups = {}
        for module_id, group_id, count in module_group_counts:
            if module_id not in module_max_groups:
                module_max_groups[module_id] = {"group_id": group_id, "count": count}
            elif count > module_max_groups[module_id]["count"]:
                module_max_groups[module_id] = {"group_id": group_id, "count": count}
        
        # Calculate compliance scores
        module_compliance_list = []
        total_non_compliant = 0
        total_compliant = 0
        total_alerts_overall = 0
        
        for module_data in module_compliance_data:
            module_id = module_data.module_id
            non_compliant = module_data.non_compliant_count or 0
            compliant = module_data.compliant_count or 0
            total = module_data.total_alerts
            
            # Calculate compliance percentage
            if total > 0:
                compliance_percentage = round((non_compliant / total) * 100, 1)
            else:
                compliance_percentage = 0
            
            # Get max alerts group info
            max_group_info = module_max_groups.get(module_id, {"group_id": None, "count": 0})
            max_group_id = max_group_info["group_id"]
            max_group_alerts = max_group_info["count"]
            
            # Create dynamic response based on grouping flag
            compliance_item = {
                "module_id": module_id,
                "module_name": module_map.get(module_id, "Unknown"),
                "compliance_percentage": compliance_percentage,
                "total_alerts": total,
                "compliant_alerts": compliant,
                "non_compliant_alerts": non_compliant,
                f"max_alerts_{group_id_field}": max_group_id if max_group_id else 0,
                f"max_alerts_{group_name_field}": group_map.get(max_group_id, "Unknown" if max_group_id else "Unassigned"),
                f"max_alerts_{breakdown}_count": max_group_alerts
            }
            
            module_compliance_list.append(compliance_item)
            
            total_compliant += compliant
            total_non_compliant += non_compliant
            total_alerts_overall += total
        
        # Calculate overall compliance score
        if total_alerts_overall > 0:
            overall_compliance = round((total_non_compliant / total_alerts_overall) * 100)
        else:
            overall_compliance = 0
    
        return {
            "overall_compliance_score": overall_compliance,
            "total_alerts": total_alerts_overall,
            "total_compliant": total_compliant,
            "total_non_compliant": total_non_compliant,
            "breakdown": breakdown ,
            "module_compliance_data": module_compliance_list
        }
        
    except Exception as e:
        print("Error in fetching overall compliance score:", e)
        return {
            "overall_compliance_score": 0,
            "total_alerts": 0,
            "total_compliant": 0,
            "total_non_compliant": 0,
            "breakdown": breakdown,
            "module_compliance_data": []
        }
   

def get_dynamic_heatmap_data(data):
    """
    Dynamic heatmap API that allows users to specify X and Y axis dimensions
    Supported dimensions: modules, zones, areas, subareas
    """
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        x_axis = data.get("x_axis")  # e.g., "modules", "zones", "areas", "sub_areas"
        y_axis = data.get("y_axis")  # e.g., "modules", "zones", "areas", "sub_areas"
        
        # Validate required parameters
        if not all([client_id, factory_id, x_axis, y_axis]):
            return {"error": "client_id, factory_id, x_axis, and y_axis are required"}
        
        # Validate axis values
        valid_dimensions = ["modules", "zones", "areas", "subareas"]
        if x_axis not in valid_dimensions or y_axis not in valid_dimensions:
            return {"error": f"Invalid axis values. Supported: {valid_dimensions}"}
        
        if x_axis == y_axis:
            return {"error": "X and Y axis cannot be the same"}
        
        start_date, end_date = parse_filters(data)
        
        # Get base query for alerts
        base_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)
        
        # Get mapping data for all dimensions
        modules_map = {m.module_id: m.name for m in Modules.get_all_records()}
        zones_map = {z.zone_id: z.name for z in AstZones.get_by_client_factory(client_id, factory_id)}
        areas_map = {a.area_id: a.name for a in Area.get_records_by_client_factory(client_id, factory_id)}
        sub_areas_map = {sa.sub_area_id: sa.name for sa in SubArea.get_records_by_client_factory(client_id, factory_id)}
        
        # Build query based on selected dimensions
        join_needed = False
        
        # Determine which fields to select and if join is needed
        dimension_config = {
            "modules": {"field": "Alerts.module_id", "map": modules_map},
            "zones": {"field": "Alerts.zone_id", "map": zones_map},
            "areas": {"field": "Cameras.area_id", "map": areas_map, "join": True},
            "subareas": {"field": "Cameras.sub_area_id", "map": sub_areas_map, "join": True}
        }
        
        # Check if we need to join with Cameras table
        if x_axis in ["areas", "subareas"] or y_axis in ["areas", "subareas"]:
            join_needed = True
        
        # Build the query
        if join_needed:
            query = base_query.join(Cameras, Alerts.camera_id == Cameras.camera_id)
        else:
            query = base_query
        
        # Get the field expressions for grouping
        x_field_expr = eval(dimension_config[x_axis]["field"])
        y_field_expr = eval(dimension_config[y_axis]["field"])
        
        # Execute the grouped query
        heatmap_data = (
            query
            .with_entities(
                x_field_expr.label("x_value"),
                y_field_expr.label("y_value"),
                func.count(Alerts.alert_id).label("alert_count")
            )
            .group_by("x_value", "y_value")
            .all()
        )
        
        # Get all unique values for both axes
        x_map = dimension_config[x_axis]["map"]
        y_map = dimension_config[y_axis]["map"]
        
        # Create the heatmap matrix
        heatmap_matrix = []
        
        # Get all unique values that have data
        x_values = sorted(set([item.x_value for item in heatmap_data if item.x_value is not None]))
        y_values = sorted(set([item.y_value for item in heatmap_data if item.y_value is not None]))
        
        # Create a lookup for quick access
        data_lookup = {}
        for item in heatmap_data:
            key = f"{item.x_value}_{item.y_value}"
            data_lookup[key] = item.alert_count
        
        # Build the matrix
        for y_val in y_values:
            row = {
                # "y_id": y_val,
                "y_name": y_map.get(y_val, "Unknown") if y_val else "Unassigned",
                "data": []
            }
            
            for x_val in x_values:
                key = f"{x_val}_{y_val}"
                alert_count = data_lookup.get(key, 0)
                
                row["data"].append(alert_count)
            
            heatmap_matrix.append(row)
        
        # Create x-axis labels
        x_labels = [
            
                # "id": x_val,
                x_map.get(x_val, "Unknown") if x_val else "Unassigned"
            
            for x_val in x_values
        ]
        
        return {
            "x_axis": x_axis,
            "y_axis": y_axis,
            "x_labels": x_labels,
            "heatmap_data": heatmap_matrix,
        }
        
    except Exception as e:
        print("Error in get_dynamic_heatmap_data:", e)
        return {
            "error": f"Error generating heatmap: {str(e)}",
            "x_axis": data.get("x_axis"),
            "y_axis": data.get("y_axis"),
            "x_labels": [],
            "heatmap_data": []
        }
    

def get_accepted_records_monthly(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        filters = data.get("filters", {})
        date = filters.get("date")
        week = filters.get("week")
        month = filters.get("month")

        #Client ID and factory ID validation
        valid, error_response = validate_client_factory(client_id, factory_id)
        if not valid:
            return error_response

        # Get the last 4 weeks, months, or days based on the provided filter
        periods = []
        if week:
            year, week_num = map(int, week.split('-W'))
            for i in range(3, -1, -1):
                w = week_num - i
                if w < 1:
                    continue
                start_date = datetime.datetime.strptime(f'{year}-W{w}-1', "%Y-W%W-%w").date()
                end_date = start_date + datetime.timedelta(days=6)
                periods.append({
                    "label": f"{year}-W{w:02d}",
                    "start_date": start_date,
                    "end_date": end_date
                })
        elif month:
            year, month_num = map(int, month.split('-'))
            for i in range(3, -1, -1):
                m = month_num - i
                y = year
                while m < 1:
                    m += 12
                    y -= 1
                start_date = datetime.date(y, m, 1)
                last_day = calendar.monthrange(y, m)[1]
                end_date = datetime.date(y, m, last_day)
                periods.append({
                    "label": f"{y}-{'%02d' % m}",
                    "start_date": start_date,
                    "end_date": end_date
                })
        elif date:
            base_date = datetime.datetime.strptime(date, "%Y-%m-%d").date()
            for i in range(3, -1, -1):
                d = base_date - datetime.timedelta(days=i)
                periods.append({
                    "label": d.strftime("%Y-%m-%d"),
                    "start_date": d,
                    "end_date": d
                })
        else:
            return []

        results = []
        for period in periods:
            start_dt = datetime.datetime.combine(period["start_date"], datetime.time.min)
            end_dt = datetime.datetime.combine(period["end_date"], datetime.time.max)
            result = db.session.query(
                func.sum(case(((Alerts.techqa == True, 1)), else_=0)).label("accepted_count"),
                func.sum(case(((Alerts.techqa == False, 1)), else_=0)).label("rejected_count")
            ).filter(
                Alerts.client_id == client_id,
                Alerts.factory_id == factory_id,
                Alerts.timestamp >= start_dt,
                Alerts.timestamp <= end_dt
            ).first()

            accepted_count = result.accepted_count or 0
            rejected_count = result.rejected_count or 0
            results.append({
                "date": period["label"] if date else None,
                "week": period["label"] if week else None,
                "month": period["label"] if month else None,
                "accepted_records": accepted_count if accepted_count > 0 else 0,
                "rejected_records": rejected_count if rejected_count > 0 else 0,
                "date_range": {
                    "start_date": str(period["start_date"]),
                    "end_date": str(period["end_date"])
                }
            })
        return results
    except Exception as e:
        print("Exception:", e)
        return []
    

# def get_compliance_data_for_modules(
#     safety_area=None,
#     shift=None,
#     start_date=None,
#     end_date=None,
#     week=None,
#     month=None,
#     factory_id=None,
#     factory_name="",
# ):
#     try:

#         return {
#                     "totalAlertsChart": [
#                         {
#                             "category": "Helmet",
#                             "value": 2
#                         },
#                         {
#                             "category": "Vest",
#                             "value": 6
#                         },
#                         {
#                             "category": "MMHE",
#                             "value": 0
#                         },
#                         {
#                             "category": "Emergency Exit",
#                             "value": 0
#                         },
#                         {
#                             "category": "No Go",
#                             "value": 13
#                         }
#                     ]
#                 }

#     except Exception as e:
#         print(f"Error in get_compliance_data: {e}")
#         traceback.print_exc()
#         return {"totalAlertsChart": []}

def get_compliance_data_for_modules(data):
    """
    Returns the compliance data for total alerts chart for a factory.
    Optimized: Single query for all modules.
    """
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        filters = data.get("filters", {})
        area_ids = filters.get("area_ids", [])
        start_date, end_date = parse_filters(data)

        # Validate client and factory
        valid, error_response = validate_client_factory(client_id, factory_id)
        if not valid:
            return error_response

        factory_modules = Factory_Modules.get_modules_by_factory_id(factory_id)
        module_id_name_map = {m["module_id"]: m["name"] for m in factory_modules}
        module_ids = list(module_id_name_map.keys())

        # Build base query
        query = db.session.query(
            Alerts.module_id,
            func.count(Alerts.alert_id).label("count")
        ).filter(
            Alerts.client_id == client_id,
            Alerts.factory_id == factory_id,
            Alerts.module_id.in_(module_ids),
            Alerts.compliant == False,
            Alerts.timestamp >= start_date,
            Alerts.timestamp <= end_date
        )

        # Apply area filter if provided
        if area_ids:
            query = query.join(Cameras, Alerts.camera_id == Cameras.camera_id)
            query = query.filter(Cameras.area_id.in_(area_ids))

        query = query.group_by(Alerts.module_id)
        results = query.all()

        # Prepare result map for fast lookup
        module_count_map = {r.module_id: r.count for r in results}

        total_alerts_chart = []
        for module_id, module_name in module_id_name_map.items():
            count = module_count_map.get(module_id, 0)
            total_alerts_chart.append({
                "category": module_name,
                "value": count
            })

        return {"totalAlertsChart": total_alerts_chart}

    except Exception as e:
        print(f"Exception in get_compliance_summary_data: {e}")
        traceback.print_exc()
        return {"totalAlertsChart": []}
    


def get_compliance_accuracy_data(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        filters = data.get("filters", {})
        area_ids = filters.get("area_ids", [])

        start_date, end_date = parse_filters(data)

        factory_modules = Factory_Modules.get_modules_by_factory_id(factory_id)
        module_id_name_map = {m["module_id"]: m["name"] for m in factory_modules}

        query = db.session.query(
            Alerts.module_id,
            func.count(Alerts.alert_id).label("alerts"),
            func.count(Alerts.alert_id).filter(Alerts.techqa == True).label("techqa_true"),
            func.count(Alerts.alert_id).filter(Alerts.techqa == False).label("techqa_false"),
            func.count(Alerts.alert_id).filter(Alerts.aitechqa == True).label("aitechqa_true"),
            func.count(Alerts.alert_id).filter(Alerts.aitechqa == False).label("aitechqa_false"),
            func.sum(case((Alerts.compliant == True, 1), else_=0)).label("compliance")
        ).filter(
            Alerts.factory_id == factory_id,
            Alerts.client_id == client_id,
            Alerts.timestamp >= start_date,
            Alerts.timestamp <= end_date,
        )


        if area_ids:
            query = query.join(Cameras, Alerts.camera_id == Cameras.camera_id)
            query = query.filter(Cameras.area_id.in_(area_ids))

        query = query.group_by(Alerts.module_id)
        results = query.all()


        totalAlertsChart = {}
        for module_id, module_name in module_id_name_map.items():

            record = next((r for r in results if r.module_id == module_id), None)
            result_true_percentage = 0
            ai_result_true_percentage = 0

            if record:
                alerts = record.alerts or 0
                techqa_true = record.techqa_true or 0
                techqa_false = record.techqa_false or 0
                aitechqa_true = record.aitechqa_true or 0
                aitechqa_false = record.aitechqa_false or 0
                compliance = record.compliance or 0

                if alerts == 0 and compliance > 0:
                    result_true_percentage = 100
                    ai_result_true_percentage = 100

                elif alerts > 0:

                    if techqa_true == 0 and techqa_false == 0:
                        result_true_percentage = 100

                    if aitechqa_true == 0 and aitechqa_false == 0:
                        ai_result_true_percentage = 100

                    if techqa_true or techqa_false:
                        result_true_percentage = float(f"{(techqa_true / alerts) * 100:.2f}")

                    if aitechqa_true or aitechqa_false:
                        ai_result_true_percentage = float(f"{(aitechqa_true / alerts) * 100:.2f}")

            totalAlertsChart[module_name] = {
                "result_true": result_true_percentage,
                "ai_result_true": ai_result_true_percentage,
            }

        return totalAlertsChart

    except Exception as e:
        print(f"Error in get_compliance_accuracy_data: {e}")
        return {}



def get_cameras_compliance(data):
    """
    Returns compliance summary by camera, grouped by area and subarea.
    """
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        filters = data.get("filters", {})
        area_ids = filters.get("area_ids", [])
        start_date, end_date = parse_filters(data)

        # Get all areas and subareas for the factory
        areas = Area.get_records_by_client_factory(client_id, factory_id)
        sub_areas = SubArea.get_records_by_client_factory(client_id, factory_id)

        # Get all cameras for the factory, optionally filter by area_ids
        cameras = Cameras.get_records_by_client_factory_id(client_id, factory_id)
        if area_ids:
            cameras = [cam for cam in cameras if cam.area_id in area_ids]

        # Get factory modules for mapping
        factory_modules = Factory_Modules.get_modules_by_factory_id(factory_id)
        module_id_name_map = {m["module_id"]: m["name"] for m in factory_modules}

        # Prepare camera -> modules mapping
        camera_modules_map = {}
        for cam in cameras:
            # Assuming cam.modules is a list of module_ids
            module_names = [module_id_name_map.get(mid, "Unknown") for mid in getattr(cam, "modules", [])]
            camera_modules_map[cam.camera_id] = module_names

        # Query compliance and violation counts per camera
        alert_query = db.session.query(
            Cameras.area_id,
            Cameras.sub_area_id,
            Cameras.camera_id,
            func.sum(case((Alerts.compliant == True, 1), else_=0)).label("compliance"),
            func.sum(case((Alerts.compliant == False, 1), else_=0)).label("violations")
        ).join(Cameras, Alerts.camera_id == Cameras.camera_id
        ).filter(
            Alerts.factory_id == factory_id,
            Alerts.client_id == client_id,
            Alerts.timestamp >= start_date,
            Alerts.timestamp <= end_date,
            Cameras.active == True
        )
        if area_ids:
            alert_query = alert_query.filter(Cameras.area_id.in_(area_ids))
        alert_query = alert_query.group_by(Cameras.area_id, Cameras.sub_area_id, Cameras.camera_id)
        alert_results = alert_query.all()

        # Structure: area_id -> {sub_area_id -> [camera info]}
        area_dict = {}
        for area in areas:
            owner_record = UserAreaOwner.get_by_area_id(area.area_id)
            owner_id = owner_record[0].user_id if owner_record else None
            owner_name = Users.get_name_by_user_id(owner_id) if owner_id else ""
            area_dict[area.area_id] = {
                "AreaName": area.name,
                "AreaId": area.area_id,
                "AreaOwner": owner_name,
                "AreaOwnerId": owner_id,
                "ImagePath": getattr(area, "image_path", ""),
                "SubAreas": []
            }

        # Subarea mapping for quick lookup
        subarea_name_map = {sa.sub_area_id: sa.name for sa in sub_areas}

        # Camera info per subarea
        subarea_cameras = defaultdict(list)
        # For compliance percentage calculation per area
        area_compliance_stats = defaultdict(lambda: {"compliance": 0, "violations": 0})

        for row in alert_results:
            area_id, sub_area_id, camera_id, compliance, violations = row
            subarea_cameras[(area_id, sub_area_id)].append({
                "SubAreaName": subarea_name_map.get(sub_area_id, ""),
                "Cameras": camera_id,
                "Compliance": int(compliance),
                "Violations": int(violations),
                "Modules": camera_modules_map.get(camera_id, [])
            })
            area_compliance_stats[area_id]["compliance"] += int(compliance)
            area_compliance_stats[area_id]["violations"] += int(violations)

        # Build final response
        response = []
        for area_id, area_info in area_dict.items():
            subareas = []
            for sub_area_id in [sa.sub_area_id for sa in sub_areas if sa.area_id == area_id]:
                subareas.extend(subarea_cameras.get((area_id, sub_area_id), []))
            total_comp = area_compliance_stats[area_id]["compliance"]
            total_vio = area_compliance_stats[area_id]["violations"]
            area_compliance = round((total_comp / (total_comp + total_vio) * 100) if (total_comp + total_vio) > 0 else 100, 1)
            area_info.update({
                "AreaCompliance": area_compliance,
                "SubAreas": subareas
            })
            response.append(area_info)

        return response

    except Exception as e:
        print(f"Error in get_cameras_compliance: {e}")
        return []



# ╔════════════════════════════════╗
# ║ UNIFIED APIs END               ║
# ╚════════════════════════════════╝


# ╔══════════════════════════════════════════════════╗
# ║ TALKPOOL: SPECIFIC APIs START                    ║
# ╚══════════════════════════════════════════════════╝


def get_stats(data):
    try:
        client_id = data.get("client_id")
        factory_id = data.get("factory_id")
        module_ids = data.get("module_ids", [])
        zone_ids = data.get("zone_ids", [])

        start_date, end_date = parse_filters(data)

        # --- Total cameras from CamerasliveFeed ---
        total_cameras = db.session.query(
            func.count(func.distinct(CamerasliveFeed.camera_id))
        ).filter(
            CamerasliveFeed.client_id == client_id,
            CamerasliveFeed.factory_id == factory_id
        ).scalar() or 0

        print("Total cameras found:", total_cameras)

        # --- Active cameras from CamerasliveFeed ---
        cutoff = datetime.datetime.now() - timedelta(minutes=15)

        # Get latest timestamp for each camera
        subquery = db.session.query(
            CamerasliveFeed.camera_id,
            func.max(CamerasliveFeed.timestamp).label('latest')
        ).filter(
            CamerasliveFeed.client_id == client_id,
            CamerasliveFeed.factory_id == factory_id
        ).group_by(CamerasliveFeed.camera_id).subquery()

        # Now count cameras whose latest timestamp is within cutoff
        active_cameras = db.session.query(func.count()).select_from(subquery).filter(
            subquery.c.latest >= cutoff
        ).scalar() or 0

        print("Active cameras found:", active_cameras)

        # Total non-compliant alerts
        base_query = get_filtered_alerts(client_id, factory_id, start_date, end_date)
        total_alerts = base_query.count()
        print("Total alerts found:", total_alerts)

        # Get all modules for mapping
        modules = Modules.get_all_records()
        module_name_map = {m.name.lower(): m.module_id for m in modules}

        # PPE alerts
        ppe_module_id = module_name_map.get("ppe")
        ppe_alerts = (
            base_query.filter(Alerts.module_id == ppe_module_id).count()
            if ppe_module_id else 0
        )
        print("PPE alerts found:", ppe_alerts)

        # Unauthorize/Working on Site alerts
        unauth_module_id = module_name_map.get("Unathorise_Access")
        working_on_site_module_id = module_name_map.get("working on site")

        if unauth_module_id:
            unauthorize_alerts = base_query.filter(Alerts.module_id == unauth_module_id).count()
            working_on_site_alerts = 0
        elif working_on_site_module_id:
            unauthorize_alerts = 0
            working_on_site_alerts = base_query.filter(Alerts.module_id == working_on_site_module_id).count()
        else:
            unauthorize_alerts = 0
            working_on_site_alerts = 0

        print("Unauthorize alerts found:", unauthorize_alerts)
        print("Working on site alerts found:", working_on_site_alerts)

        return {
            "total_cameras": total_cameras,
            "active_cameras": active_cameras,
            "total_alerts": total_alerts,
            "active_ai_models": 5,
            "ppe_alerts": ppe_alerts,
            "unauthorize_alerts": unauthorize_alerts,
            "working_on_site_alerts": working_on_site_alerts
        }
    except Exception as e:
        print("Error in get_stats:", e)
        return {
            "total_cameras": 0,
            "active_cameras": 0,
            "total_alerts": 0,
            "active_ai_models": 0,
            "ppe_alerts": 0,
            "unauthorize_alerts": 0,
            "working_on_site_alerts": 0
        }
    



# ╔══════════════════════════════════════════════════╗
# ║ TALKPOOL: SPECIFIC APIs END                      ║
# ╚══════════════════════════════════════════════════╝
