from api.models.assetModels import Client, Factory, Cameras, CamerasliveFeed
from api.models.facialModels import *
from api import db
# from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta
from api.utils.utils import parse_filters
import calendar
from sqlalchemy import or_, and_, func, case
from sqlalchemy.orm import joinedload, selectinload



def get_live_logs(data):
    """
    Get live facial recognition logs based on filters

    Args:
        data (dict): Contains filter parameters like client_id, factory_id, etc.

    Returns:
        list: List of facial recognition logs
    """
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    limit = data.get("limit", 20)
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")

    # Use parse_filters to get start_date and end_date from filters
    start_date, end_date = parse_filters(data)

    # Start with base query
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    )

    # Apply additional filters if provided
    if camera_id:
        query = query.filter_by(camera_id=camera_id)

    if employee_id:
        query = query.filter_by(employee_id=employee_id)

    # Apply date range filter
    query = query.filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    # Order by detection time, most recent first
    query = query.order_by(FacialRecognitionLogs.detection_datetime.desc())

    # Limit the number of results
    if limit == 0 or limit is None or limit == "": 
        logs = query.all()
    else:
        try:
            limit_int = int(limit)
            logs = query.limit(limit_int).all()
        except Exception:
            logs = query.all()

    # Format the results
    result = []
    for log in logs:
        camera_obj = Cameras.get_by_id(log.camera_id)
        employee_obj = FacialEmployees.get_by_employee_id(log.employee_id)
        result.append({
            "log_id": log.log_id,
            "client_id": log.client_id,
            "client_name": Client.get_name_by_id(log.client_id),
            "factory_id": log.factory_id,
            "factory_name": Factory.get_name_by_id(log.factory_id),
            "employee_id": log.employee_id,
            "employee_name": employee_obj.name if employee_obj else None,
            "blacklisted": employee_obj.blacklisted if employee_obj else None,
            "employee_designation": employee_obj.designation if employee_obj else None,
            "camera_id": log.camera_id,
            "camera_name": camera_obj.camera_name if camera_obj else None,
            "camera_ip": camera_obj.camera_ip if camera_obj else None,
            "camera_position_no": camera_obj.camera_position_no if camera_obj else None,
            "detection_datetime": log.detection_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "employee_id": log.person_detected,
            "sop_followed": log.sop_followed,
            "client_verified": log.client_verified,
            "ai_verified": log.ai_verified,
            "face_image_url": log.face_image_url,
            "confidence": log.confidence if hasattr(log, 'confidence') else None
        })

    return result

def get_live_logss(data):
    """
    Get live facial recognition logs based on filters with optimized performance

    Args:
        data (dict): Contains filter parameters like client_id, factory_id, etc.

    Returns:
        dict: Paginated facial recognition logs and stats
    """
    # Extract parameters with defaults
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    limit = data.get("limit", 20)
    offset = data.get("offset", 0)
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")
    camera_position_no = data.get("camera_position_no")

    # Get date range from filters
    start_date, end_date = parse_filters(data)

    # Start with base query with only essential filters
    base_query = FacialRecognitionLogs.query.filter(
        FacialRecognitionLogs.client_id == client_id,
        FacialRecognitionLogs.factory_id == factory_id,
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    # Apply additional filters if provided
    if camera_id:
        base_query = base_query.filter(FacialRecognitionLogs.camera_id == camera_id)
    if employee_id:
        base_query = base_query.filter(FacialRecognitionLogs.employee_id == employee_id)
    if camera_position_no:
        # Use exists() instead of fetching all camera IDs first
        base_query = base_query.filter(
            exists().where(
                (Cameras.camera_id == FacialRecognitionLogs.camera_id) &
                (Cameras.camera_position_no == camera_position_no)
            )
        )

    # Create a count query for total logs (without pagination)
    count_query = base_query.with_entities(func.count(FacialRecognitionLogs.log_id))

    # Get aggregation counts in a single query
    agg_query = base_query.with_entities(
        func.count(FacialRecognitionLogs.log_id).label("total_logs"),
        func.sum(case((FacialRecognitionLogs.ai_verified == True, 1), else_=0)).label("total_ai_verified"),
        func.sum(case((FacialRecognitionLogs.ai_verified == False, 1), else_=0)).label("total_ai_rejected"),
        func.sum(case((FacialRecognitionLogs.client_verified == True, 1), else_=0)).label("total_client_verified"),
        func.sum(case((FacialRecognitionLogs.client_verified == False, 1), else_=0)).label("total_client_rejected"),
    )

    # Execute the aggregation query
    agg_counts = agg_query.first()
    total_logs = agg_counts.total_logs or 0
    total_ai_verified = agg_counts.total_ai_verified or 0
    total_ai_rejected = agg_counts.total_ai_rejected or 0
    total_client_verified = agg_counts.total_client_verified or 0
    total_client_rejected = agg_counts.total_client_rejected or 0

    # Calculate alerts with a more efficient query
    alert_query = base_query.join(
        FacialEmployees,
        FacialRecognitionLogs.employee_id == FacialEmployees.employee_id,
        isouter=True
    ).filter(
        or_(
            FacialEmployees.blacklisted == True,
            FacialRecognitionLogs.employee_id.in_(["ytm7_unknown", "IFL_unknown"]),
            FacialRecognitionLogs.sop_followed == False
        )
    )

    total_alerts = db.session.query(func.count()).select_from(alert_query.subquery()).scalar() or 0

    # Apply ordering and pagination
    paginated_query = base_query.order_by(FacialRecognitionLogs.detection_datetime.desc())
    
    try:
        limit_int = int(limit) if limit and str(limit).isdigit() else None
        offset_int = int(offset) if offset and str(offset).isdigit() else 0
        
        if limit_int:
            logs = paginated_query.offset(offset_int).limit(limit_int).all()
        else:
            logs = paginated_query.offset(offset_int).all()
    except Exception:
        logs = paginated_query.all()

    # Batch load related objects to avoid N+1 queries
    camera_ids = {log.camera_id for log in logs}
    employee_ids = {log.employee_id for log in logs}
    
    # Prefetch cameras and employees
    cameras = {cam.camera_id: cam for cam in Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()}
    employees = {emp.employee_id: emp for emp in FacialEmployees.query.filter(FacialEmployees.employee_id.in_(employee_ids)).all()}
    
    # Prefetch client and factory names in bulk if needed
    client_names = {}
    factory_names = {}
    if logs:
        unique_client_ids = {log.client_id for log in logs}
        unique_factory_ids = {log.factory_id for log in logs}
        
        # Batch fetch client names
        client_names = {c.client_id: c.name for c in Client.query.filter(Client.client_id.in_(unique_client_ids)).all()}
        
        # Batch fetch factory names
        factory_names = {f.factory_id: f.name for f in Factory.query.filter(Factory.factory_id.in_(unique_factory_ids)).all()}

    # Format results
    result = []
    for log in logs:
        camera = cameras.get(log.camera_id)
        employee = employees.get(log.employee_id)
        
        result.append({
            "log_id": log.log_id,
            "client_id": log.client_id,
            "client_name": client_names.get(log.client_id),
            "factory_id": log.factory_id,
            "factory_name": factory_names.get(log.factory_id),
            "employee_id": log.employee_id,
            "employee_name": employee.name if employee else None,
            "blacklisted": employee.blacklisted if employee else None,
            "employee_designation": employee.designation if employee else None,
            "camera_id": log.camera_id,
            "camera_name": camera.camera_name if camera else None,
            "camera_ip": camera.camera_ip if camera else None,
            "camera_position_no": camera.camera_position_no if camera else None,
            "detection_datetime": log.detection_datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "person_detected": log.person_detected,
            "sop_followed": log.sop_followed,
            "client_verified": log.client_verified,
            "ai_verified": log.ai_verified,
            "face_image_url": log.face_image_url,
            "confidence": getattr(log, 'confidence', None)
        })

    return {
        "logs": result,
        "total_logs": total_logs,
        "total_alerts": total_alerts,
        "total_ai_verified": total_ai_verified,
        "total_client_verified": total_client_verified,
        "total_client_rejected": total_client_rejected,
        "total_ai_rejected": total_ai_rejected,
    }

def get_stats(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")
    user_type = data.get("user_type", "").lower() if data.get("user_type") else None

    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)

    # Base query for logs
    log_query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    )

    if camera_id:
        log_query = log_query.filter_by(camera_id=camera_id)
    if employee_id:
        log_query = log_query.filter_by(employee_id=employee_id)

    # Apply date range filter
    log_query = log_query.filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    # Apply user_type filter to logs if specified
    if user_type == "contractor":
        log_query = log_query.filter(
            FacialRecognitionLogs.employee_id.ilike("IFL_contractor%")
        )
    elif user_type == "unknown":
        log_query = log_query.filter(
            FacialRecognitionLogs.employee_id.ilike("IFL_unknown%")
        )
    elif user_type == "employee":
        log_query = log_query.filter(
            ~FacialRecognitionLogs.employee_id.ilike("IFL_unknown%"),
            ~FacialRecognitionLogs.employee_id.ilike("IFL_contractor%")
        )

    logs = log_query.all()

    # Prepare camera_id to position mapping
    camera_ids = list(set([log.camera_id for log in logs]))
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()
    camera_position_map = {cam.camera_id: cam.camera_position_no for cam in cameras}

    total_entries = 0
    total_exits = 0
    total_unknown_alerts = 0
    sop_followed = 0
    sop_unfollowed = 0

    for log in logs:
        position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
        if position == "gate in":
            total_entries += 1
        elif position == "gate out":
            total_exits += 1

        if getattr(log, "employee_id", "").lower().startswith("ifl_unknown"):
            total_unknown_alerts += 1

        if getattr(log, "sop_followed", None) is True:
            sop_followed += 1
        elif getattr(log, "sop_followed", None) is False:
            sop_unfollowed += 1

    # Employee type counts
    employee_query = FacialEmployees.query.filter(
        FacialEmployees.client_id == client_id,
        FacialEmployees.factory_id == factory_id
    )

    # Apply user_type filter if specified
    if user_type == "contractor":
        employee_query = employee_query.filter(
            FacialEmployees.employee_id.ilike("IFL_contractor%")
        )
    elif user_type == "unknown":
        employee_query = employee_query.filter(
            FacialEmployees.employee_id.ilike("IFL_unknown%")
        )
    elif user_type == "employee":
        employee_query = employee_query.filter(
            ~FacialEmployees.employee_id.ilike("IFL_unknown%"),
            ~FacialEmployees.employee_id.ilike("IFL_contractor%")
        )

    employees = employee_query.all()

    # Initialize counters
    total_employees = 0
    total_contractors = 0
    total_unknown_persons = 0
    total_blacklisted = 0

    for emp in employees:
        emp_id = emp.employee_id.lower()
        if emp_id.startswith("ifl_unknown"):
            total_unknown_persons += 1
        elif emp_id.startswith("ifl_contractor"):
            total_contractors += 1
        else:
            total_employees += 1
        
        if getattr(emp, "blacklisted", False):
            total_blacklisted += 1

    # Prepare response data
    response_data = {
        "total_entries": total_entries,
        "total_exits": total_exits,
        "total_unknown_alerts": total_unknown_alerts,
        "sop_followed": sop_followed,
        "sop_unfollowed": sop_unfollowed,
        "total_blacklisted": total_blacklisted
    }

    # Include all counts if no user_type filter, otherwise only include the filtered type
    if not user_type:
        response_data.update({
            "total_employees": total_employees,
            "total_contractors": total_contractors,
            "total_unknown_persons": total_unknown_persons
        })
    else:
        if user_type == "employee":
            response_data["total_employees"] = total_employees
        elif user_type == "contractor":
            response_data["total_contractors"] = total_contractors
        elif user_type == "unknown":
            response_data["total_unknown_persons"] = total_unknown_persons

    return response_data

def get_sop_stats(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")

    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)

    # Base query
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    )

    if camera_id:
        query = query.filter_by(camera_id=camera_id)
    if employee_id:
        query = query.filter_by(employee_id=employee_id)

    # Apply date range filter
    query = query.filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )

    logs = query.all()
    if not logs:
        # Return zeroed stats if no logs
        return {
            "employee": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
            "contractor": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
            "unknown": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
            "blacklisted": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}}
        }

    # Prefetch all employees for this client/factory and map by employee_id
    all_employees = FacialEmployees.query.filter_by(client_id=client_id, factory_id=factory_id).all()
    emp_map = {emp.employee_id: emp for emp in all_employees}

    # Prepare camera_id to position mapping
    camera_ids = list(set([log.camera_id for log in logs]))
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all() if camera_ids else []
    camera_position_map = {cam.camera_id: cam.camera_position_no for cam in cameras}

    # Initialize stats
    stats = {
        "employee": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
        "contractor": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
        "unknown": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}},
        "blacklisted": {"entries": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}, "exits": {"sop_followed": 0, "sop_unfollowed": 0, "total": 0}}
    }

    for log in logs:
        emp = emp_map.get(log.employee_id)
        emp_type = "employee"
        emp_id = log.employee_id or ""
        if emp:
            if emp.blacklisted:
                emp_type = "blacklisted"
            elif "IFL_unknown" in emp_id:
                emp_type = "unknown"
            elif "IFL_contractor" in emp_id:
                emp_type = "contractor"
        else:
            if "IFL_unknown" in emp_id:
                emp_type = "unknown"
            elif "IFL_contractor" in emp_id:
                emp_type = "contractor"
            else:
                emp_type = "employee"

        position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
        if position == "gate in":
            stats[emp_type]["entries"]["total"] += 1
            if getattr(log, "sop_followed", None) is True:
                stats[emp_type]["entries"]["sop_followed"] += 1
            elif getattr(log, "sop_followed", None) is False:
                stats[emp_type]["entries"]["sop_unfollowed"] += 1
        elif position == "gate out":
            stats[emp_type]["exits"]["total"] += 1
            if getattr(log, "sop_followed", None) is True:
                stats[emp_type]["exits"]["sop_followed"] += 1
            elif getattr(log, "sop_followed", None) is False:
                stats[emp_type]["exits"]["sop_unfollowed"] += 1

    return stats

def get_daily_movement_summary(data):
    """
    Get daily movement summary with entries, exits, unknown alerts, and currently inside count
    
    Args:
        data (dict): Contains filter parameters like client_id, factory_id, filters, etc.
    
    Returns:
        dict: Daily movement summary data
    """
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    newFilters = data.get('filters', {})
    # Determine filter_type based on non-empty value in payload
    print("dataaaa", data)
    if newFilters.get("date"):
        filter_type = "date"
    elif newFilters.get("week"):
        filter_type = "week"
    elif newFilters.get("month"):
        filter_type = "month"
    else:
        filter_type = "week"  # default
    
    # Parse date filters using the utility function
    start_date, end_date = parse_filters(data)
    
    # Base query for facial recognition logs
    query = FacialRecognitionLogs.query.filter_by(
        client_id=client_id,
        factory_id=factory_id
    ).filter(
        FacialRecognitionLogs.detection_datetime >= start_date,
        FacialRecognitionLogs.detection_datetime <= end_date
    )
    
    logs = query.all()
    
    # Get camera position mapping
    camera_ids = list(set([log.camera_id for log in logs]))
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()
    camera_position_map = {cam.camera_id: cam.camera_position_no for cam in cameras}
    # ...existing code...
    daily_summary = {}

    for log in logs:
        if filter_type == "date":
            hour = log.detection_datetime.hour
            hour_str = str(hour)
            if hour_str not in daily_summary:
                daily_summary[hour_str] = {
                    "hour": hour,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                }
            # Count unknown alerts
            if getattr(log, "employee_id", "").lower() == "ifl_unknown":
                daily_summary[hour_str]["unknown_alerts"] += 1
            print("Detected:", getattr(log, "person_detected", "N/A"))
            # Count entries and exits
            position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
            if position == "gate in":
                daily_summary[hour_str]["total_entries"] += 1
            elif position == "gate out":
                daily_summary[hour_str]["total_exits"] += 1

        elif filter_type == "month":
            day = log.detection_datetime.day
            day_str = str(day)
            if day_str not in daily_summary:
                daily_summary[day_str] = {
                    "day": day,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                }
            # Count unknown alerts
            if getattr(log, "employee_id", "").lower() == "ifl_unknown":
                daily_summary[day_str]["unknown_alerts"] += 1
            # Count entries and exits
            position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
            if position == "gate in":
                daily_summary[day_str]["total_entries"] += 1
            elif position == "gate out":
                daily_summary[day_str]["total_exits"] += 1

        else:  # week (default)
            day_name = log.detection_datetime.strftime("%A")
            if day_name not in daily_summary:
                daily_summary[day_name] = {
                    "day": day_name,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                }
            if getattr(log, "employee_id", "").lower() == "ifl_unknown":
                daily_summary[day_name]["unknown_alerts"] += 1
            position = camera_position_map.get(log.camera_id, "").lower() if camera_position_map.get(log.camera_id) else ""
            if position == "gate in":
                daily_summary[day_name]["total_entries"] += 1
            elif position == "gate out":
                daily_summary[day_name]["total_exits"] += 1

    # Calculate currently_inside for each group
    if filter_type == "date":
        for hour_str in daily_summary:
            entries = daily_summary[hour_str]["total_entries"]
            exits = daily_summary[hour_str]["total_exits"]
            daily_summary[hour_str]["currently_inside"] = max(0, entries - exits)
    elif filter_type == "month":
        for day_str in daily_summary:
            entries = daily_summary[day_str]["total_entries"]
            exits = daily_summary[day_str]["total_exits"]
            daily_summary[day_str]["currently_inside"] = max(0, entries - exits)
    else:
        for day_name in daily_summary:
            entries = daily_summary[day_name]["total_entries"]
            exits = daily_summary[day_name]["total_exits"]
            daily_summary[day_name]["currently_inside"] = max(0, entries - exits)

 
    # Convert to list and ensure all days of week are present
    weekdays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
  
    result = []
    print('filtersType', filter_type)
    if filter_type == "week":
        weekdays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        for day in weekdays:
            if day in daily_summary:
                result.append(daily_summary[day])
            else:
                result.append({
                    "day": day,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                    "currently_inside": 0
                })
        return result

    elif filter_type == "date":
        # Fill missing hours with zero data
        for hour in range(1, 25):
            hour_str = str(hour)
            if hour_str in daily_summary:
                result.append(daily_summary[hour_str])
            else:
                result.append({
                    "hour": hour,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                    "currently_inside": 0
                })
        return result

    elif filter_type == "month":
        today = datetime.today()
        last_day = calendar.monthrange(today.year, today.month)[1]
        for day in range(1, last_day + 1):
            day_str = str(day)
            if day_str in daily_summary:
                result.append(daily_summary[day_str])
            else:
                result.append({
                    "day": day,
                    "unknown_alerts": 0,
                    "total_entries": 0,
                    "total_exits": 0,
                    "currently_inside": 0
                })
        return result
    
    # return result    

def camera_status(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    camera_id = data.get("camera_id")
    employee_id = data.get("employee_id")

    start_date, end_date = parse_filters(data)

    # Step 1: Get livefeed cameras
    livefeed_query = CamerasliveFeed.query.filter_by(client_id=client_id, factory_id=factory_id)
    if camera_id:
        livefeed_query = livefeed_query.filter_by(camera_id=camera_id)
    livefeed_cameras = livefeed_query.all()
    livefeed_map = {lf.camera_id: lf for lf in livefeed_cameras}
    camera_ids = list(livefeed_map.keys())

    # Step 2: Fetch all camera details in one go
    cameras = Cameras.query.filter(Cameras.camera_id.in_(camera_ids)).all()
    camera_map = {c.camera_id: c for c in cameras}

    # Step 3: Fetch all relevant logs in one go
    logs_query = FacialRecognitionLogs.query.filter(
        FacialRecognitionLogs.client_id == client_id,
        FacialRecognitionLogs.factory_id == factory_id,
        FacialRecognitionLogs.camera_id.in_(camera_ids),
        FacialRecognitionLogs.detection_datetime.between(start_date, end_date)
    )
    if employee_id:
        logs_query = logs_query.filter(FacialRecognitionLogs.employee_id == employee_id)

    logs = logs_query.all()

    # Step 4: Fetch all employees once
    employee_ids = {log.employee_id for log in logs if log.employee_id and log.employee_id != "IFL_unknown"}
    employees = FacialEmployees.query.filter(FacialEmployees.employee_id.in_(employee_ids)).all()
    blacklisted_employees = {e.employee_id for e in employees if e.blacklisted}

    # Step 5: Organize logs by camera
    from collections import defaultdict
    logs_by_camera = defaultdict(list)
    for log in logs:
        logs_by_camera[log.camera_id].append(log)

    camera_status_list = []
    for cam_id, cam_logs in logs_by_camera.items():
        cam = camera_map.get(cam_id)
        if not cam:
            continue
        livefeed = livefeed_map.get(cam_id)

        total_logs = len(cam_logs)
        blacklisted = sum(1 for log in cam_logs if log.employee_id in blacklisted_employees)
        sop_followed = sum(1 for log in cam_logs if log.sop_followed is True)
        sop_unfollowed = sum(1 for log in cam_logs if log.sop_followed is False)
        total_violations = sum(1 for log in cam_logs if log.employee_id == "IFL_unknown")

        gate_type = "Other"
        position = (cam.camera_position_no or "").strip().lower()
        if position == "gate in":
            gate_type = "Gate In"
        elif position == "gate out":
            gate_type = "Gate Out"

        camera_status_list.append({
            "camera_id": cam.camera_id,
            "camera_name": cam.camera_name,
            "camera_position_no": cam.camera_position_no,
            "total_logs": total_logs,
            "blacklisted": blacklisted,
            "sop_followed": sop_followed,
            "sop_unfollowed": sop_unfollowed,
            "total_violations": total_violations,
            "gate_type": gate_type,
            "image_url": getattr(livefeed, "image_url", None)
        })

    gate_in_stats = [c for c in camera_status_list if c["gate_type"] == "Gate In"]
    gate_out_stats = [c for c in camera_status_list if c["gate_type"] == "Gate Out"]

    return {
        "gate_in": gate_in_stats,
        "gate_out": gate_out_stats
    }

def get_employes(data):
    client_id = data.get("client_id")
    factory_id = data.get("factory_id")
    user_type = data.get("user_type", "")
    employee_id = data.get("employee_id", "")

    # Base query
    query = FacialEmployees.query.filter_by(client_id=client_id, factory_id=factory_id)

    # Filtering logic
    if user_type == "":
        pass
    elif user_type.lower() == "unknown":
        query = query.filter(FacialEmployees.employee_id.ilike("%IFL_unknown%"))
    elif user_type.lower() == "contractor":
        query = query.filter(FacialEmployees.employee_id.ilike("%IFL_contractor%"))
    elif user_type.lower() == "blacklisted":
        query = query.filter(FacialEmployees.blacklisted == True)
    elif user_type.lower() == "employee":
        query = query.filter(
            ~FacialEmployees.employee_id.ilike("%IFL_unknown%"),
            ~FacialEmployees.employee_id.ilike("%IFL_contractor%"),
    )
    if employee_id:
        query = query.filter(FacialEmployees.employee_id == employee_id)

    employees = query.all()

    result = []
    for emp in employees:
        emp_dict = {
            "employee_id": emp.employee_id,
            "client_id": emp.client_id,
            "factory_id": emp.factory_id,
            "name": emp.name,
            "designation": emp.designation,
            "blacklisted": bool(emp.blacklisted) if emp.blacklisted is not None else False,
            # Add other fields as needed
        }
        # Get first face_image_url from FacialRecognitionLogs for this employee
        log = FacialRecognitionLogs.query.filter_by(
            employee_id=emp.employee_id,
            client_id=client_id,
            factory_id=factory_id
        ).order_by(FacialRecognitionLogs.detection_datetime.asc()).first()
        emp_dict["face_image_url"] = log.face_image_url if log else None

        result.append(emp_dict)

    return result
    
