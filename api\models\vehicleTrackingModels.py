from api import db
import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
import datetime
from sqlalchemy import desc, func

class VehicleTrackingMaterial(db.Model):
    __tablename__ = 'vehicle_tracking_material'
    __table_args__ = (
        db.Index('idx_vehicle_tracking_client_factory_material_id', 'client_id', 'factory_id', 'material_id'),
    )
    material_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    name = db.Column(db.Text, nullable=False)
    material_type = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            new_record = cls(
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                name=data.get("name"),
                material_type=data.get("material_type", ""),
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False
    
class VehicleTracking(db.Model):
    __tablename__ = 'vehicle_tracking'
    __table_args__ = (
        db.Index('idx_vehicle_tracking_client_factory_movement_type', 'client_id', 'factory_id', 'movement_type'),
        db.Index('idx_vehicle_tracking_client_factory_local_db_id', 'client_id', 'factory_id', 'local_db_id'),
    )
    id = db.Column(db.Integer, primary_key=True)
    local_db_id = db.Column(db.Integer)   #AI machine local database id
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    movement_type = db.Column(db.Text, nullable=False)  #entrance, exit
    number_plate = db.Column(db.Text, nullable=True)
    vehicle_type = db.Column(db.Text, nullable=True)   #Container, car, 16-wheeler
    material_id = db.Column(db.Integer, db.ForeignKey('vehicle_tracking_material.material_id'))
    image_url = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_id)).first()
        return latest_row.local_db_id if latest_row else None 
        
    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
    
    @classmethod
    def get_records_by_movement_type(cls, movement_type):
        return cls.query.filter_by(movement_type=movement_type).all()
        
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            new_record = cls(
                local_db_id=data.get("id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                movement_type=data.get("movement_type"),
                number_plate=data.get("number_plate", ""),
                vehicle_type=data.get("vehicle_type", ""),
                image_url=data.get("image_url"),
                timestamp=data.get("timestamp")
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False
        

class VehicleShipmentAnalysis(db.Model):
    __tablename__ = 'vehicle_shipment_analysis'
    __table_args__ = (
        db.Index('idx_vehicle_tracking_client_factory_local_db_id', 'client_id', 'factory_id', 'local_db_id'),
    )
    id = db.Column(db.Integer, primary_key=True)
    local_db_id = db.Column(db.Integer)   #AI machine local database id
    client_id = db.Column(db.Integer, db.ForeignKey('ast_client.client_id'))
    factory_id = db.Column(db.Integer, db.ForeignKey('ast_factory.factory_id'))
    material = db.Column(db.Text, nullable=False)  
    timestamp = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    
    
    @classmethod
    def get_local_db_id(cls,client_id,factory_id):
        latest_row= cls.query.filter_by(client_id=client_id, factory_id=factory_id).order_by(desc(cls.local_db_id)).first()
        return latest_row.local_db_id if latest_row else None 
        
    @classmethod
    def get_by_id(cls, id):
        return cls.query.filter_by(id=id).first()
    
    @classmethod
    def get_records_by_movement_type(cls, movement_type):
        return cls.query.filter_by(movement_type=movement_type).all()
        
    @classmethod
    def get_records_by_client_factory_id(cls, client_id, factory_id):
        return cls.query.filter_by(client_id=client_id, factory_id=factory_id).all()
        
    @classmethod
    def get_all_records(cls):
        return cls.query.all()
        
    @classmethod
    def add_multiple_rows(cls,dataset):
        rows = []

        for data in dataset:
            new_record = cls(
                local_db_id=data.get("id"),
                client_id=data.get("client_id"),
                factory_id=data.get("factory_id"),
                material=data.get("material"),
                timestamp=data.get("timestamp")
            )
            rows.append(new_record)

        if rows:
            db.session.add_all(rows)
            db.session.commit()
            return True
        return False