from sqlalchemy.exc import ProgrammingError
from sqlalchemy import text
from api import db
from api.models.uamModels import *
from api import config_parser
from werkzeug.security import generate_password_hash


def print_password_hash(password):
    
    hash_pass = generate_password_hash(password, method="pbkdf2")
    print(f"Hash for {password} -> {hash_pass}")
    

def setup_database():
    """
    Ensures model tables are created,
    only if it doesn't already exist as one.
    
    Args:
        db: The SQLAlchemy database instance from your Flask app.
    """
    # Ensure your model tables are created
    db.create_all()
    print("Create db.create_all executed")
    

def init_default_data(app):
    with app.app_context():
        # Check if the database is already populated
        if not Role.query.first() and not Permission.query.first():
            # Create default roles
            admin_role = Role(role_name='Admin', description='Administrator with full access')
            manager_role = Role(role_name='Manager', description='Administrator with full access')
            user_role = Role(role_name='User', description='Regular user with limited access')
            db.session.add(admin_role)
            db.session.add(manager_role)
            db.session.add(user_role)
            db.session.commit()

            # Create default permissions
            read_permission = Permission(permission_name='Read', description='Read access')
            write_permission = Permission(permission_name='Write', description='Write access')
            delete_permission = Permission(permission_name='Delete', description='Delete access')
            user_crud_permission = Permission(permission_name='UserCRUD', description='CRUD access to the user database')
            db.session.add(read_permission)
            db.session.add(write_permission)
            db.session.add(delete_permission)
            db.session.add(user_crud_permission)
            db.session.commit()

            # Assign permissions to roles
            admin_role_permissions = [
                RolePermission(role_id=admin_role.role_id, permission_id=read_permission.permission_id),
                RolePermission(role_id=admin_role.role_id, permission_id=write_permission.permission_id),
                RolePermission(role_id=admin_role.role_id, permission_id=delete_permission.permission_id),
                RolePermission(role_id=admin_role.role_id, permission_id=user_crud_permission.permission_id),
            ]
            manager_role_permissions = [
                RolePermission(role_id=manager_role.role_id, permission_id=read_permission.permission_id),
                RolePermission(role_id=manager_role.role_id, permission_id=write_permission.permission_id),
                RolePermission(role_id=manager_role.role_id, permission_id=delete_permission.permission_id),
                RolePermission(role_id=manager_role.role_id, permission_id=user_crud_permission.permission_id),
            ]
            user_role_permissions = [
                RolePermission(role_id=user_role.role_id, permission_id=read_permission.permission_id),
                RolePermission(role_id=user_role.role_id, permission_id=user_crud_permission.permission_id),
            ]
            db.session.add_all(admin_role_permissions + manager_role_permissions + user_role_permissions)
            db.session.commit()

            # Create default users
            super_admin_email = config_parser.get('ADMIN', 'email')
            super_admin_password = config_parser.get('ADMIN', 'password')
            
            # Check if the super admin user already exists
            if not Users.query.filter_by(email=super_admin_email).first():
                super_admin_user = Users(
                    name='admin',
                    email=super_admin_email,
                    password=generate_password_hash(super_admin_password)
                )
                db.session.add(super_admin_user)
                db.session.commit()

                # Assign roles to the super admin user
                admin_role_id = admin_role.role_id
                super_admin_user_role = UserRole(user_id=super_admin_user.user_id, role_id=admin_role_id)
                db.session.add(super_admin_user_role)
                db.session.commit()

            # Create manager account
            manager_email = config_parser.get('Manager', 'email')
            manager_password = config_parser.get('Manager', 'password')
            
            # Check if the super admin user already exists
            if not Users.query.filter_by(email=manager_email).first():
                manager_user = Users(
                    name='manager',
                    email=manager_email,
                    password=generate_password_hash(manager_password)
                )
                db.session.add(manager_user)
                db.session.commit()

                # Assign roles to the super admin user
                manager_role_id = manager_role.role_id
                manager_user_role = UserRole(user_id=manager_user.user_id, role_id=manager_role_id)
                db.session.add(manager_user_role)
                db.session.commit()
            print("Default roles, permissions, and super admin user initialized.")
