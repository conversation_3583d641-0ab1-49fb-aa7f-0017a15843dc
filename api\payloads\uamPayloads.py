from api import uam_ns
from flask_restx import fields
from werkzeug.datastructures import FileStorage

# Payload Model
send_mail_payload_new = uam_ns.model(
    "Send Mail",
    {
        "SES": fields.Boolean(
            required=False,
            description="Send using AWS SES if True, else use SMTP.",
            default=False,
        ),
        "receivers": fields.List(
            fields.String,
            required=True,
            description="List of recipients' email addresses.",
        ),
        "cc_emails": fields.List(
            fields.String,
            required=False,
            description="List of CC email addresses.",
        ),
        "subject": fields.String(
            required=True,
            description="Email subject.",
        ),
        "body": fields.String(
            required=True,
            description="Email body content.",
        ),
    },
)


# USERS
login_user_paylaod = uam_ns.model(
    "Login User With Email",
    {
        "email": fields.String(
            default="<EMAIL>",
            required=True,
            description="The associated email address",
        ),
        "password": fields.String(
            default="neduet33iacc44&",
            required=True,
            description="The associated password",
        ),
    },
)

# Activity Log Payload
activity_log_payload = uam_ns.model(
    'Activity Log Payload',
    {
        "username":fields.String(
            default = '',
            description = 'This username is used for filtering actions performed by a specific user.'
        )
    }
)

# Nested Model for Email Notifications
email_notification_model = uam_ns.model(
    "EmailNotification",
    {
        "toggle": fields.Boolean(
            required=True, description="Toggle state for email notifications"
        ),
        "areas": fields.List(
            fields.Integer, required=True, description="List of area IDs"
        ),
        "sub_areas": fields.List(
            fields.Integer, required=True, description="List of sub area IDs"
        ),
        "alert_emails": fields.List(
            fields.String,
            required=True,
            description="Comma-separated email addresses for alerts",
            default=["<EMAIL>"],
        ),
    },
)

# Nested Model for WhatsApp Notifications
whatsapp_notification_model = uam_ns.model(
    "WhatsAppNotification",
    {
        "toggle": fields.Boolean(
            required=True, description="Toggle state for WhatsApp notifications"
        ),
        "areas": fields.List(
            fields.Integer, required=True, description="List of area IDs"
        ),
        "sub_areas": fields.List(
            fields.Integer, required=True, description="List of sub area IDs"
        ),
        "phone_numbers": fields.List(
            fields.String,
            required=True,
            description="Comma-separated phone numbers for WhatsApp alerts",
            default=["1234567890"],
        ),
    },
)

add_user_paylaod = uam_ns.model(
    "Add New User",
    {   
        "role_id": fields.Integer(
            default=1, required=True, description="The user role Id"
        ),
        "factory_id": fields.Integer(
            default=1, required=True, description="The Factory Id"
        ),
        "client_id": fields.Integer(
            default=1, required=True, description="The Client Id"
        ),
        "name": fields.String(
            default="Ahmed", required=True, description="The associated user name"
        ),
        "email": fields.String(
            default="<EMAIL>",
            required=True,
            description="The associated email address",
        ),
        "mobile_no": fields.String(
            default="1234-567890",
            required=False,
            description="The user's mobile phone number",
        ),
        "password": fields.String(
            default="ItOfficer@12", required=True, description="The associated password"
        ),
         "areas": fields.List(
            fields.Integer, required=False, description="List of area IDs"
        ),
        "email_notifications": fields.Nested(
            email_notification_model,
            required=False,
            description="Settings for email notifications",
        ),
        "whatsapp_notifications": fields.Nested(
            whatsapp_notification_model,
            required=False,
            description="Settings for WhatsApp notifications",
        ),
    },
)

update_user_paylaod = uam_ns.model(
    "Update User",
    {
        "admin_id": fields.Integer(default=1, required=True, description="The admin user ID"),
        "user_id": fields.Integer(default=1, required=True, description="The user ID"),
        "role_id": fields.Integer(
            default=1, required=True, description="The user role Id"
        ),
        "name": fields.String(
            default="Ahmed", required=True, description="The associated user name"
        ),
        "email": fields.String(
            default="<EMAIL>",
            required=True,
            description="The associated email address",
        ),
        "mobile_no": fields.String(
            default="1234-567890",
            required=False,
            description="The user's mobile phone number",
        ),
        "factories": fields.List(
            fields.Integer, required=True, description="List of factories IDs"
        ),
        "areas": fields.List(
            fields.Integer, description="List of area IDs"
        ),
        "email_notifications": fields.Nested(
            email_notification_model,
            required=False,
            description="Settings for email notifications",
        ),
        "whatsapp_notifications": fields.Nested(
            whatsapp_notification_model,
            required=False,
            description="Settings for WhatsApp notifications",
        ),
    },
)

user_model = uam_ns.model(
    "User Model",
    {
        "name": fields.String(required=True, description="The name of the person"),
        "email": fields.String(required=True, description="The email of the person"),
    },
)

add_multiple_users_payload = uam_ns.model(
    "Add Multiple Users Payload",
    {
        "admin_id": fields.Integer(default=1, required=True, description="The user ID"),
        "role_id": fields.Integer(
            required=True, description="The role associated with the user"
        ),
        "users": fields.List(
            fields.Nested(user_model),  # Use the user_model here
            required=True,
            description="List of users",
        ),
    },
)


# ROLE
add_role_paylaod = uam_ns.model(
    "Add New Role",
    {
        "role_name": fields.String(
            default="IT", required=True, description="The user role"
        ),
        "description": fields.String(
            default="User with limited access",
            required=True,
            description="The role description",
        ),
    },
)

update_role_paylaod = uam_ns.model(
    "Update New Role",
    {
        "role_id": fields.Integer(default=1, required=True, description="The role ID"),
        "role_name": fields.String(
            default="IT", required=True, description="The user role"
        ),
        "description": fields.String(
            default="User with limited access",
            required=True,
            description="The role description",
        ),
    },
)

add_role_permission_paylaod = uam_ns.model(
    "Add Role Permission",
    {
        "role_id": fields.Integer(
            default=1, required=True, description="The associated Role ID"
        ),
        "permission_id": fields.Integer(
            default=1, required=True, description="The associated permission ID"
        ),
    },
)

delete_role_permission_paylaod = uam_ns.model(
    "Delete Role Permission",
    {
        "role_id": fields.Integer(
            default=1, required=True, description="The associated Role ID"
        ),
        "permission_id": fields.Integer(
            default=1, required=True, description="The associated permission ID"
        ),
    },
)


